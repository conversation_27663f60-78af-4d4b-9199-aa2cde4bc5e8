// pages/foldshare/messageDetail/messageDetail.js
// 获取全局应用实例
const app = getApp();

// 引入角色管理器
import roleManager from '../../../utils/roleManager';
import eventBus from '../../../utils/eventBus';
import { loginManager } from '../../../utils/loginManager';

// 引入新的导航工具函数
const { navigateBack } = require('../../../utils/navigation');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    showModal:false,
    isReply: false,
    isFlipped: false,
    commentSectionVisible: false,
    message: null,
    abc: [255, 255, 255],
    content: '',
    comment: [],
    page: 1,
    isLoading: false,
    isSubmitting: false, // 添加发送状态
    hasMoreComments: true, // 是否还有更多评论
    post_id:'',
    likedComments: {},
    likedReplies: {},
    targetCommentId: null,
    targetReplyId: null,
    showEmoji: false,
    showEmojiList: false,
    keyboardHeight: 0,
    sortType: 'likes',
    timeOrder: 'desc',
    likesOrder: 'desc',
    userInfo: {
      id: '',
      username: '',
      face_url: '',
      status: ''
    },
    emojiList: ['😊','😂','🤣','😅','😍','🥰','😘','😋','😎','😭','😤','🙄','😴','🤔','🤗','🤫','😏','😒','🙂','🙃','😉','😌','😜','🤪','😝','🤑','🤗','🤭','🤫','🤔'],
    quickEmojiList: ['😊','😂','🤣','😅','😍','🥰','😎','😋'],
    emojiConfig: {
      isLoaded: true,
      list: Array.from({length: 228}, (_, i) => ({
        id: i + 1,
        url: `/packageEmoji/emoji/emoji${i + 1}.png`
      }))
    },
    tempImages: [],
    showGuideHint: false,
    guideStep: 1,
    totalGuideSteps: 2,
    guideClosing: false,
    voteSelection: {},
    hasVoteSelection: false,
    hasScrolled: false,
    lastScrollPosition: 0,
    currentCommentId: '',
    newCommentId: '',
    isHighlighting: false,
    expandedComments: {},
    repliesPerLoad: 3,
    defaultRepliesShow: 2,
    showSharePanel: false,
    showShareBtn: false,
    showCustomActionSheet: false,
    canDelete: false,
    longPressCommentId: null,
    longPressReplyId: null
  },
  dianji(e) {
    if (roleManager.hasBasePermission()) {
      // 检查是否有联系方式
      const message = this.data.message;
      const hasContactInfo = message && (message.vx || message.qq || message.phonenumber);
      
      if (hasContactInfo) {
        this.setData({
          showModal: true,
        });
      } else {
        wx.showToast({
          title: '该帖子未提供联系方式',
          icon: 'none',
          duration: 2000
        });
      }
    } else {
      wx.showModal({
        title: '提示',
        content: '为保护大家的隐私，联系方式需要认证后才能查看',
        confirmText: '去认证',
        cancelText: '取消',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/fold3/student/student'
            });
          }
        }
      });
    }
  },
  
  onReplyClick(event) {
    const commentId = event.currentTarget.dataset.id;
    const username = event.currentTarget.dataset.username;
    const type = event.currentTarget.dataset.type;
    const userId = event.currentTarget.dataset.userid;
    const replyId = event.currentTarget.dataset.replyid;

    // 验证必要参数
    if (!commentId || !username || !type || !userId) {
      console.error('回复参数不完整:', { commentId, username, type, userId, replyId });
      wx.showToast({
        title: '回复参数不完整',
        icon: 'none'
      });
      return;
    }

    // 获取当前页面滚动位置
    wx.createSelectorQuery().selectViewport().scrollOffset(res => {
      this.setData({
        lastScrollPosition: res.scrollTop,
        currentCommentId: commentId
      });
    }).exec();

    // 对于匿名帖子，需要获取匿名名称
    let displayUsername = username;
    if (this.data.message && this.data.message.is_anonymous_display) {
      // 如果是匿名帖子，使用匿名名称
      if (type === 'reply' && replyId) {
        // 查找对应的回复数据获取匿名名称
        const targetComment = this.data.comment.find(comment => comment.id === commentId);
        if (targetComment && targetComment.replies) {
          const targetReply = targetComment.replies.find(reply => reply.id === replyId);
          if (targetReply && targetReply.anonymous_name) {
            displayUsername = targetReply.anonymous_name;
          }
        }
      } else {
        // 查找对应的评论数据获取匿名名称
        const targetComment = this.data.comment.find(comment => comment.id === commentId);
        if (targetComment && targetComment.anonymous_name) {
          displayUsername = targetComment.anonymous_name;
        }
      }
    }

    // 设置回复状态，确保所有字段都有有效值
    this.setData({
      isReply: true,
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${displayUsername}`,
      content: '',
      post_id: commentId,  // 父评论ID
      replyToUserId: userId || '',  // 被回复用户ID，确保不是undefined
      replyToUsername: displayUsername || '',  // 被回复用户名，使用匿名名称
      replyType: type || 'comment',  // 回复类型：comment或reply
      replyToPostId: (type === 'reply' && replyId) ? replyId : null  // 被回复的回复ID
    });
  },
  onCommentInput(event) {
    const value = event.detail.value;
    const lines = value.split('\n').length;
    
    // 计算内容的实际高度（假设每行20rpx）
    const contentHeight = lines * 20;
    
    // 如果内容高度超过初始高度（60rpx），将textarea切换到expanded状态
    const textareaClass = contentHeight > 60 ? 'comment-textarea expanded' : 'comment-textarea';
    
    this.setData({
      content: value,
      textareaClass: textareaClass
    });
  },
  // 处理评论点击，显示评论框并唤醒键盘
  handleCommentClick() {
    // 保存当前滚动位置
    wx.createSelectorQuery().selectViewport().scrollOffset(res => {
      this.setData({
        lastScrollPosition: res.scrollTop
      });
    }).exec();
    
    this.setData({
      isReply: false,
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: '说点什么...',
      content: '',
      showEmoji: false,
      showEmojiList: false,
      keyboardHeight: 0
    });
  },
  // 处理输入框失去焦点
  handleInputBlur() {
    setTimeout(() => {
      // 只有当表情面板都没显示时，才重置状态
      if (!this.data.showEmoji && !this.data.showEmojiList) {
        this.setData({
          commentInputFocused: false,
          keyboardHeight: 0
        });
      }
    }, 100);
  },

  // 处理输入框获取焦点
  handleInputFocus() {
    this.setData({
      showEmoji: false,
      showEmojiList: false,
      commentInputFocused: true,
      commentSectionVisible: true  // 确保评论区域可见
    });
  },

  // 处理键盘高度变化
  handleKeyboardHeightChange(e) {
    const height = e.detail.height;
    if (height > 0) {
      // 键盘弹出时，关闭表情面板
      this.setData({
        keyboardHeight: height,
        showEmoji: false,
        showEmojiList: false,
        commentSectionVisible: true
      });
    } else {
      // 键盘收起时，保持当前状态
      this.setData({
        keyboardHeight: height
      });
    }
  },

  // 隐藏评论区域
  hideCommentSection() {
    this.setData({
      commentSectionVisible: false,
      showEmoji: false,
      showEmojiList: false,
      keyboardHeight: 0,
      commentInputFocused: false,
      content: ''
    });
  },

  // ========== 匿名组件事件处理 ==========

  // 匿名帖子点赞
  onPostLike(e) {
    const postData = e.detail.postData;
    // 调用原有的点赞方法
    this.dolike();
  },

  // 匿名帖子评论
  onPostComment(e) {
    const postData = e.detail.postData;
    // 调用原有的评论方法
    this.handleCommentClick();
  },

  // 匿名帖子图片点击
  onPostImageClick(e) {
    const { url, index, images } = e.detail;
    // 调用原有的图片预览方法
    this.handleImageClick({
      currentTarget: {
        dataset: { url, index }
      }
    });
  },

  // 匿名帖子长按
  onPostLongPress(e) {
    const postData = e.detail.postData;
    // 调用原有的长按方法
    this.showManageOptions();
  },

  // 匿名帖子联系按钮
  onPostContact(e) {
    const postData = e.detail.postData;
    // 调用原有的联系方法
    this.dianji();
  },

  // 匿名评论回复
  onCommentReply(e) {
    const commentData = e.detail.commentData;
    // 对于匿名评论，我们需要特殊处理
    // 因为匿名评论可能没有真实的user_id
    this.onReplyClick({
      currentTarget: {
        dataset: {
          id: commentData.id,
          username: commentData.anonymous_name || commentData.username,
          type: 'comment',
          userid: commentData.original_user_id || commentData.user_id || 0
        }
      }
    });
  },

  // 匿名评论点赞
  onCommentLike(e) {
    const commentData = e.detail.commentData;
    // 调用原有的评论点赞方法
    this.dolike2({
      currentTarget: {
        dataset: {
          id: commentData.id
        }
      }
    });
  },

  // 匿名评论图片点击
  onCommentImageClick(e) {
    const { url, index, images } = e.detail;
    // 调用原有的图片预览方法
    this.handleCommentImageClick({
      currentTarget: {
        dataset: { url, images }
      }
    });
  },

  // 匿名评论长按
  onCommentLongPress(e) {
    const commentData = e.detail.commentData;
    // 调用原有的长按方法
    this.showCommentActionSheet({
      currentTarget: {
        dataset: {
          comment: commentData
        }
      }
    });
  },

  // 匿名评论回复的回复
  onReplyToReply(e) {
    const { commentData, replyData } = e.detail;
    // 调用原有的回复方法
    this.onReplyClick({
      currentTarget: {
        dataset: {
          id: commentData.id,
          username: replyData.anonymous_name || replyData.username,
          type: 'reply',
          userid: replyData.original_user_id || replyData.user_id,
          replyid: replyData.id
        }
      }
    });
  },

  // 匿名回复长按
  onReplyLongPress(e) {
    const { replyData } = e.detail;
    // 调用原有的回复长按方法
    this.showReplyActionSheet({
      currentTarget: {
        dataset: {
          reply: replyData
        }
      }
    });
  },

  // 匿名回复点赞
  onReplyLike(e) {
    const { replyData, commentData } = e.detail;
    // 调用原有的回复点赞方法
    this.dolike3({
      currentTarget: {
        dataset: {
          replyid: replyData.id,
          commentid: commentData.id
        }
      }
    });
  },

  // 匿名回复图片点击
  onReplyImageClick(e) {
    const { url, images } = e.detail;
    // 调用原有的回复图片预览方法
    this.handleReplyImageClick({
      currentTarget: {
        dataset: { url, images }
      }
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    try {
      // 获取用户信息
      const userId = wx.getStorageSync('user_id');
      const username = wx.getStorageSync('username') || '';
      const face_url = wx.getStorageSync('face_url') || '';
      
      const userInfo = {
        id: userId,
        username: username,
        face_url: face_url
      };
      
      // 设置默认数据
      this.setData({
        userInfo: userInfo,
        messageId: options.id || '',
        page: 1, // 添加页码初始化
        comment: [], // 初始化评论列表
        hasMoreComments: true, // 重置为有更多数据
        guideStep: 0, // 默认不显示引导
        isHighlighting: false,
        lastScrollPosition: 0,
        highlightedCommentId: 0,
        highlightedReplyId: 0,
        targetCommentId: options.comment_id || null,
        targetReplyId: options.reply_id || null,
        commentType: options.type || '',
        repliesPerLoad: 3, // 每次加载3条回复
        sortType: 'likes', // 修改默认排序方式为点赞数
        timeOrder: 'desc',
        likesOrder: 'desc',
        message: {
          choose: parseInt(options.choose) || 0 // 添加choose参数
        },
        originalChoose: parseInt(options.choose) || 0 // 保存原始choose参数
      });



      // 检查引导状态
      this.checkGuideStatus();

      // 加载消息详情
      if (options.id) {
        this.loadMessageDetails(options.id, () => {
          // 加载完消息详情后获取评论列表
          this.getCommentList(options.id, (success) => {
            if (success) {
              // 如果有目标评论或回复ID，执行滚动
              if (this.data.targetReplyId) {
                setTimeout(() => {
                  this.scrollToReply();
                }, 800);
              } else if (this.data.targetCommentId) {
                setTimeout(() => {
                  this.scrollToComment();
                }, 800);
              }
            }
          });
        });
      } else {
        wx.showToast({
          title: '参数错误，无法加载消息',
          icon: 'none',
          duration: 3000
        });
        // 延迟返回上一页，让用户看到错误提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      wx.showToast({
        title: '页面加载失败: ' + (error.message || '未知错误'),
        icon: 'none',
        duration: 3000
      });
      // 不要自动返回，让用户看到错误信息
    }
  },
  
  /**
   * 页面滚动事件处理函数
   * 这是Page对象的生命周期函数，不需要手动注册
   */
  onPageScroll(e) {
    // 只有在不处于高亮状态时才更新滚动位置
    if (!this.data.isHighlighting) {
      this.setData({
        lastScrollPosition: e.scrollTop
      });
    }
  },
  
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时的清理工作
    // 清理定时器，避免内存泄漏
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
      this.loadingTimeout = null;
    }
    // 删除错误的移除滚动监听代码，因为onPageScroll是生命周期函数，不需要手动移除
    // wx.offPageScroll(this.onPageScroll);
  },

  /**
   * 加载消息详情
   */
  loadMessageDetails: function(messageId, callback) {
    try {
      if (!messageId) {
        console.error('loadMessageDetails: messageId is required');
        wx.showToast({
          title: '消息加载失败',
          icon: 'none'
        });
        if (callback && typeof callback === 'function') {
          callback(false);
        }
        return;
      }

      this.setData({ isLoading: true });

      wx.request({
        url: app.globalData.wangz + '/message/getMessageDetails',
        method: 'POST',
        data: {
          id: messageId
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'token': wx.getStorageSync('access_token')
        },
        success: (res) => {
          try {
            if (!res.data || res.data.error_code !== 0) {
              console.error('消息详情返回错误:', res.data?.msg);
              wx.showToast({
                title: '消息加载失败: ' + (res.data?.msg || '未知错误'),
                icon: 'none'
              });
              this.setData({ isLoading: false });
              if (callback && typeof callback === 'function') {
                callback(false);
              }
              return;
            }

            const messageData = res.data.data;
            if (!messageData) {
              console.error('服务器返回的消息数据为空');
              wx.showToast({
                title: '消息数据为空',
                icon: 'none'
              });
              this.setData({ isLoading: false });
              if (callback && typeof callback === 'function') {
                callback(false);
              }
              return;
            }

            // 处理图片数据
            const imageUtil = require('../../../utils/imageUtil.js');
            if (messageData.images) {
              messageData.images = imageUtil.processImageArray(messageData.images);
            } else {
              messageData.images = [];
            }

            // 处理头像路径
            if (messageData.face_url) {
              messageData.face_url = imageUtil.processAvatarUrl(messageData.face_url);
            }

            // 处理投票数据
            if (messageData.has_vote && messageData.vote) {
              // 确保vote是对象
              if (typeof messageData.vote === 'string') {
                try {
                  messageData.vote = JSON.parse(messageData.vote);
                } catch (e) {
                  console.error('解析投票JSON失败:', e);
                  messageData.vote = null;
                  messageData.has_vote = false;
                }
              }

              // 如果vote是有效对象，处理投票选项和统计数据
              if (messageData.vote && messageData.vote.options) {
                // 确保options是数组
                if (typeof messageData.vote.options === 'string') {
                  try {
                    messageData.vote.options = JSON.parse(messageData.vote.options);
                  } catch (e) {
                    console.error('解析投票选项JSON失败:', e);
                    messageData.vote.options = [];
                  }
                }

                // 处理投票统计
                if (!messageData.vote.statistics) {
                  messageData.vote.statistics = {
                    total_voters: 0,
                    options_count: Array(messageData.vote.options.length).fill(0)
                  };
                }

                // 计算每个选项的进度条宽度
                if (messageData.vote.statistics && messageData.vote.statistics.total_voters) {
                  messageData.vote.options = messageData.vote.options.map((option, index) => {
                    const count = messageData.vote.statistics.options_count[index] || 0;
                    const total = messageData.vote.statistics.total_voters || 0;
                    const progressWidth = total > 0 ? (count / total * 100) : 0;
                    
                    if (typeof option === 'string') {
                      return { text: option, progressWidth };
                    } else {
                      return { ...option, progressWidth };
                    }
                  });
                } else {
                  messageData.vote.options = messageData.vote.options.map(option => {
                    if (typeof option === 'string') {
                      return { text: option, progressWidth: 0 };
                    } else {
                      return { ...option, progressWidth: 0 };
                    }
                  });
                }

                // 确保user_choices字段存在
                if (!messageData.vote.user_choices) {
                  messageData.vote.user_choices = {};
                } else if (typeof messageData.vote.user_choices === 'string') {
                  // 如果user_choices是字符串，尝试解析成对象
                  try {
                    messageData.vote.user_choices = JSON.parse(messageData.vote.user_choices);
                  } catch (e) {
                    messageData.vote.user_choices = {};
                  }
                }
                
                // 确保user_choices格式正确
                if (Array.isArray(messageData.vote.user_choices)) {
                  // 如果是数组格式，转换为对象格式
                  const choices = {};
                  messageData.vote.user_choices.forEach(index => {
                    choices[index] = true;
                  });
                  messageData.vote.user_choices = choices;
                }
              }
            }

            // 使用后端返回的choose参数，如果没有则使用URL参数
            if ((messageData.choose === null || messageData.choose === undefined || messageData.choose === '') && this.data.originalChoose) {
              messageData.choose = this.data.originalChoose;
            }

            // 确保choose是数字类型
            if (messageData.choose !== null && messageData.choose !== undefined) {
              messageData.choose = parseInt(messageData.choose);
            }



            // 更新界面数据
            this.setData({
              message: messageData,
              isLoading: false
            });


            if (callback && typeof callback === 'function') {
              callback(true);
            }
          } catch (error) {
            console.error('处理消息详情数据错误:', error);
            wx.showToast({
              title: '消息数据处理失败',
              icon: 'none'
            });
            this.setData({ isLoading: false });
            if (callback && typeof callback === 'function') {
              callback(false);
            }
          }
        },
        fail: (error) => {
          console.error('获取消息详情请求失败:', error);
          wx.showToast({
            title: '消息加载失败，请检查网络',
            icon: 'none'
          });
          this.setData({ isLoading: false });
          if (callback && typeof callback === 'function') {
            callback(false);
          }
        }
      });
    } catch (error) {
      console.error('loadMessageDetails函数执行错误:', error);
      wx.showToast({
        title: '消息加载失败',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      if (callback && typeof callback === 'function') {
        callback(false);
      }
    }
  },

  handleImageClick(e) {
    const url = e.currentTarget.dataset.url;
    const index = e.currentTarget.dataset.index;
    const images = this.data.message.images || [];
    
    if (images.length === 0) {
      // 如果没有图片，直接返回
      return;
    }

    wx.previewImage({
      current: url, // 当前显示图片的链接，这里使用用户点击的图片
      urls: images, // 需要预览的图片链接列表
      success: () => {
        // 图片预览成功
      },
      fail: (err) => {
        console.error('图片预览失败:', err);
        wx.showToast({
          title: '图片预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 添加评论图片预览方法
  handleCommentImageClick(e) {
    // 获取当前评论的所有图片
    const commentImages = e.currentTarget.dataset.images;
    const currentUrl = e.currentTarget.dataset.url;
    
    wx.previewImage({
      current: currentUrl,
      urls: commentImages,
      fail: (err) => {
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 添加回复图片预览方法
  handleReplyImageClick(e) {
    // 获取当前回复的所有图片
    const replyImages = e.currentTarget.dataset.images;
    const currentUrl = e.currentTarget.dataset.url;
    
    wx.previewImage({
      current: currentUrl,
      urls: replyImages,
      fail: (err) => {
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },

  onClickLeft() {
    navigateBack();
  },

  /**
   * 统一点赞组件事件处理
   */
  onMessageLikeChange: function(e) {
    const { isLiked, totalLikes } = e.detail
    this.setData({
      'message.is_liked': isLiked,
      'message.total_likes': totalLikes
    })
  },

  onCommentLikeChange: function(e) {
    const { targetId, isLiked, totalLikes } = e.detail
    // 更新评论点赞状态
    const updatedComments = this.data.comment.map(item => {
      if (item.id === targetId) {
        return {
          ...item,
          is_liked: isLiked,
          total_likes: totalLikes
        };
      }
      return item;
    });
    this.setData({ comment: updatedComments });
  },

  onReplyLikeChange: function(e) {
    const { targetId, isLiked, totalLikes } = e.detail
    // 更新回复点赞状态
    this.updateReplyLikeStatus(targetId, isLiked, totalLikes);
  },

  // 更新帖子的点赞状态
  updateMessageLikeStatus: function(messageId, isLiked, totalLikes) {
    if (this.data.message && this.data.message.id === messageId) {
      this.setData({
        'message.is_liked': isLiked,
        'message.total_likes': totalLikes
      });
    }
  },

  // 更新评论的点赞状态
  updateCommentLikeStatus: function(commentId, isLiked, totalLikes) {
    const updatedComments = this.data.comment.map(item => {
      if (item.id === commentId) {
        return {
          ...item,
          is_liked: isLiked,
          total_likes: totalLikes
        };
      }
      return item;
    });
    this.setData({ comment: updatedComments });
  },

  // 更新回复的点赞状态
  updateReplyLikeStatus: function(replyId, isLiked, totalLikes) {
    const updatedComments = this.data.comment.map(comment => {
      const updatedReplies = comment.replies?.map(reply => {
        if (reply.id === replyId) {
          return {
            ...reply,
            is_liked: isLiked,
            total_likes: totalLikes
          };
        }
        return reply;
      }) || [];
      return {
        ...comment,
        replies: updatedReplies
      };
    });
    this.setData({ comment: updatedComments });
  },

  // 切换排序方式
  changeSort(e) {
    const type = e.currentTarget.dataset.type;
    const { sortType, timeOrder, likesOrder } = this.data;
    const messageId = this.data.message?.id;

    // 如果没有消息ID，直接返回
    if (!messageId) {
      wx.showToast({
        title: '无法加载评论',
        icon: 'none'
      });
      return;
    }
    
    // 设置加载状态为true
    this.setData({ isLoading: true });
    
    if (type === sortType) {
      // 如果点击的是当前排序方式，切换排序顺序
      if (type === 'time') {
        this.setData({
          timeOrder: timeOrder === 'desc' ? 'asc' : 'desc'
        });
      } else {
        this.setData({
          likesOrder: likesOrder === 'desc' ? 'asc' : 'desc'
        });
      }
    } else {
      // 切换排序方式时保持降序
      this.setData({ 
        sortType: type,
        timeOrder: 'desc',
        likesOrder: 'desc'
      });
    }

    // 重置到第一页并重新获取评论列表
    this.setData({
      page: 1,
      comment: [], // 清空当前评论列表
      hasMoreComments: true // 重置为有更多数据
    }, () => {
      this.getCommentList(messageId);
    });
  },

  // 获取评论列表
  getCommentList: function(message_id, callback) {
    try {
      if (!message_id) {
        wx.showToast({ title: '评论加载失败', icon: 'none' });
        this.setData({ isLoading: false });
        if (callback && typeof callback === 'function') {
          callback(false);
        }
        return;
      }

      this.setData({ isLoading: true });

      // 准备排序参数
      const sortType = this.data.sortType;
      const sortOrder = sortType === 'time' ? this.data.timeOrder : this.data.likesOrder;
      const userId = wx.getStorageSync('user_id');

      wx.request({
        url: app.globalData.wangz + '/comment/getComments',
        method: 'POST',
        data: {
          message_id: message_id,
          page: this.data.page,
          sort_type: sortType,
          sort_order: sortOrder,
          user_id: userId
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'token': wx.getStorageSync('access_token')
        },
        success: (res) => {
          try {
            if (!res.data || res.data.error_code !== 0) {
              console.error('获取评论列表失败:', res.data);
              wx.showToast({
                title: '评论加载失败: ' + (res.data?.msg || '未知错误'),
                icon: 'none'
              });
              this.setData({ isLoading: false });
              if (callback && typeof callback === 'function') {
                callback(false);
              }
              return;
            }

            const comments = res.data.data || [];
            
            // 异步处理评论和回复图片数据，避免阻塞主线程
            const processCommentsAsync = () => {
              return new Promise((resolve) => {
                setTimeout(() => {
                  try {
                    const imageUtil = require('../../../utils/imageUtil.js');
                    comments.forEach(comment => {
                      // 处理评论图片
                      if (comment.images && typeof comment.images === 'string') {
                        try {
                          comment.images = JSON.parse(comment.images);
                        } catch (e) {
                          comment.images = [];
                        }
                      } else if (!comment.images) {
                        comment.images = [];
                      }

                      // 处理评论图片URL
                      if (comment.images && comment.images.length > 0) {
                        comment.images = imageUtil.processImageArray(comment.images);
                      }

                      // 处理评论者头像
                      if (comment.face_url) {
                        comment.face_url = imageUtil.processAvatarUrl(comment.face_url);
                      }

                      // 处理回复
                      if (comment.replies && comment.replies.length > 0) {
                        comment.replies.forEach(reply => {
                          // 处理回复图片
                          if (reply.images && typeof reply.images === 'string') {
                            try {
                              reply.images = JSON.parse(reply.images);
                            } catch (e) {
                              console.error('解析回复图片JSON失败:', e);
                              reply.images = [];
                            }
                          } else if (!reply.images) {
                            reply.images = [];
                          }

                          // 处理回复图片URL
                          if (reply.images && reply.images.length > 0) {
                            reply.images = imageUtil.processImageArray(reply.images);
                          }

                          // 处理回复者头像
                          if (reply.face_url) {
                            reply.face_url = imageUtil.processAvatarUrl(reply.face_url);
                          }
                        });
                      }
                    });
                    resolve();
                  } catch (parseError) {
                    console.error('处理评论/回复图片数据错误:', parseError);
                    resolve();
                  }
                }, 0); // 使用 setTimeout 0 让出主线程
              });
            };

            // 异步处理数据
            processCommentsAsync().then(() => {
              try {
                // 保存原有的展开状态
                const oldExpandedComments = {...this.data.expandedComments};

                // 初始化评论展开状态
                let expandedComments = {...oldExpandedComments}; // 从现有状态复制，而不是创建空对象

                // 只为新加载的评论设置初始展开状态
                comments.forEach(comment => {
                  // 如果回复数量大于默认显示数量，需要设置展开状态
                  if (comment.replies && comment.replies.length > this.data.defaultRepliesShow) {
                    // 只有当该评论ID在现有展开状态中不存在时，才设置默认值
                    if (expandedComments[comment.id] === undefined) {
                      expandedComments[comment.id] = this.data.defaultRepliesShow; // 默认显示数量
                    }
                  }
                });

                // 分批更新数据，减少单次 setData 的负担
                const newCommentList = this.data.page === 1 ? comments : [...this.data.comment, ...comments];

                // 判断是否还有更多数据（后端每页返回10条）
                const hasMoreComments = comments.length >= 10;

                // 先更新基础数据
                this.setData({
                  isLoading: false,
                  page: this.data.page + 1,
                  hasMoreComments: hasMoreComments
                });

                // 然后更新评论数据
                setTimeout(() => {
                  this.setData({
                    comment: newCommentList,
                    commentList: newCommentList,
                    expandedComments: expandedComments
                  });
                }, 50); // 短暂延迟，让UI有时间响应

                if (callback && typeof callback === 'function') {
                  callback(true);
                }
              } catch (error) {
                console.error('处理评论列表数据错误:', error);
                wx.showToast({
                  title: '评论数据处理失败',
                  icon: 'none'
                });
                this.setData({ isLoading: false });
                if (callback && typeof callback === 'function') {
                  callback(false);
                }
              }
            });
          } catch (outerError) {
            console.error('处理评论数据外层错误:', outerError);
            wx.showToast({
              title: '评论加载失败',
              icon: 'none'
            });
            this.setData({ isLoading: false });
            if (callback && typeof callback === 'function') {
              callback(false);
            }
          }
        },
        fail: (error) => {
          console.error('获取评论列表请求失败:', error);
          wx.showToast({
            title: '评论加载失败，请检查网络',
            icon: 'none'
          });
          this.setData({ isLoading: false });
          if (callback && typeof callback === 'function') {
            callback(false);
          }
        }
      });
    } catch (error) {
      console.error('getCommentList函数执行错误:', error);
      wx.showToast({
        title: '评论加载失败',
        icon: 'none'
      });
      this.setData({ isLoading: false });
      if (callback && typeof callback === 'function') {
        callback(false);
      }
    }
  },

  submitComment() {
    if (!this.data.content.trim() && this.data.tempImages.length === 0) {
      wx.showToast({
        title: '请输入内容或选择图片',
        icon: 'none'
      });
      return;
    }

    // 检查是否正在发送中
    if (this.data.isSubmitting) {
      return;
    }

    // 设置发送状态
    this.setData({
      isSubmitting: true
    });

    // 如果有图片，先上传图片
    if (this.data.tempImages.length > 0) {
      this.uploadImages().then(imageUrls => {
        this.submitCommentWithImages(imageUrls);
      }).catch(error => {
        console.error('图片上传失败:', error);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
        // 重置发送状态
        this.setData({
          isSubmitting: false
        });
      });
    } else {
      this.submitCommentWithImages([]);
    }
  },

  // 提交带图片的评论
  submitCommentWithImages(imageUrls) {
    // 验证必要参数
    if (!getApp().globalData.user_id || !this.data.message.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      // 重置发送状态
      this.setData({
        isSubmitting: false
      });
      return;
    }

    const titlename = wx.getStorageSync('titlename') || '无';
    const titlecolor = wx.getStorageSync('titlecolor') || '0';

    const baseParams = {
      user_id: getApp().globalData.user_id,
      username: getApp().globalData.username,
      message_id: this.data.message.id,
      content: this.data.content.trim(),
      face_url: getApp().globalData.face_url,
      images: JSON.stringify(imageUrls),
      titlename: titlename,
      titlecolor: titlecolor
    };

    if (this.data.isReply) {
      // 验证回复必要参数
      if (!this.data.post_id || !this.data.replyToUserId || !this.data.replyToUsername) {
        wx.showToast({
          title: '回复参数不完整',
          icon: 'none'
        });
        // 重置发送状态
        this.setData({
          isSubmitting: false
        });
        return;
      }

      // 构建回复请求参数
      const replyParams = {
        ...baseParams,
        parent_comment_id: this.data.post_id,
        reply_type: this.data.replyType || 'comment',
        reply_to_user_id: this.data.replyToUserId,
        reply_to_username: this.data.replyToUsername,
        reply_to_post_id: this.data.replyToPostId || null
      };

      // 发送回复请求
      const token = wx.getStorageSync('access_token')
      if (!token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        // 重置发送状态
        this.setData({
          isSubmitting: false
        });
        return
      }

      wx.request({
        url: getApp().globalData.wangz + '/comment/publishNewPost',
        method: "POST",
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'token': token
        },
        data: replyParams,
        success: (res) => {
          if (res.data.error_code === 0) {
            // 获取新回复的ID和完整数据
            let newReplyId = res.data.data?.reply_id;
            let newReplyData = res.data.data?.reply_data;
            if (!newReplyId) return;

            // 优先使用后端返回的完整数据
            let newReply;
            if (newReplyData) {
              // 使用后端返回的数据（包含匿名处理）
              newReply = newReplyData;

              // 处理后端返回数据的图片和头像
              const imageUtil = require('../../../utils/imageUtil.js');
              if (newReply.images && typeof newReply.images === 'string') {
                try {
                  newReply.images = JSON.parse(newReply.images);
                } catch (e) {
                  newReply.images = [];
                }
              }
              if (newReply.images && newReply.images.length > 0) {
                newReply.images = imageUtil.processImageArray(newReply.images);
              }
              if (newReply.face_url) {
                newReply.face_url = imageUtil.processAvatarUrl(newReply.face_url);
              }
            } else {
              // 处理本地构建的数据
              const imageUtil = require('../../../utils/imageUtil.js');
              const processedImageUrls = imageUrls && imageUrls.length > 0 ?
                imageUtil.processImageArray(imageUrls) : [];
              const processedFaceUrl = imageUtil.processAvatarUrl(getApp().globalData.face_url);

              newReply = {
                id: newReplyId,
                content: this.data.content.trim(),
                user_id: getApp().globalData.user_id,
                username: getApp().globalData.username,
                face_url: processedFaceUrl,
                titlename: wx.getStorageSync('titlename') || '无',
                titlecolor: wx.getStorageSync('titlecolor') || '0',
                reply_type: this.data.replyType || 'comment',
                reply_to_user_id: this.data.replyToUserId,
                reply_to_username: this.data.replyToUsername,
                reply_to_post_id: this.data.replyToPostId || null,
                images: processedImageUrls,
                is_liked: false,
                total_likes: 0,
                send_timestamp: '刚刚'
              };
            }

            // 更新本地数据
            const updatedComments = this.data.comment.map(comment => {
              if (comment.id === this.data.post_id) {
                // 将新回复添加到回复列表的开头
                const updatedReplies = [newReply, ...(comment.replies || [])];
                return {
                  ...comment,
                  replies: updatedReplies
                };
              }
              return comment;
            });

            // 确保新回复可见，但保持现有展开状态
            const expandedComments = {...this.data.expandedComments};
            const targetComment = updatedComments.find(comment => comment.id === this.data.post_id);

            // 如果该评论的回复数量超过默认显示数量，需要设置展开状态
            if (targetComment && targetComment.replies && targetComment.replies.length > this.data.defaultRepliesShow) {
              // 如果之前没有展开状态，设置为默认显示数量
              if (expandedComments[this.data.post_id] === undefined) {
                expandedComments[this.data.post_id] = this.data.defaultRepliesShow;
              }
              // 如果新回复不在可见范围内，确保至少显示到新回复
              if (expandedComments[this.data.post_id] < 1) {
                expandedComments[this.data.post_id] = Math.max(1, this.data.defaultRepliesShow);
              }
            }

            this.setData({
              comment: updatedComments,
              expandedComments: expandedComments,
              content: '',
              tempImages: [],
              commentSectionVisible: false,
              commentInputFocused: false,
              showEmoji: false,
              showEmojiList: false,
              isReply: false,
              replyToUserId: '',
              replyToUsername: '',
              replyType: 'comment',
              replyToPostId: null,
              post_id: '',
              commentPlaceholder: '说点什么...',
              isSubmitting: false // 重置发送状态
            });


          } else {
            wx.showToast({
              title: res.data.msg || '回复失败',
              icon: 'none',
              duration: 2000
            });
            // 重置发送状态
            this.setData({
              isSubmitting: false
            });
          }
        },
        fail: (err) => {
          console.error('回复发布失败:', err);
          wx.showToast({
            title: '网络错误',
            icon: 'none',
            duration: 2000
          });
          // 重置发送状态
          this.setData({
            isSubmitting: false
          });
        }
      });
    } else {
      // 发布新评论
      const token = wx.getStorageSync('access_token')
      if (!token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        // 重置发送状态
        this.setData({
          isSubmitting: false
        });
        return
      }

      wx.request({
        url: getApp().globalData.wangz + '/comment/publishNewComment',
        method: "POST",
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'token': token
        },
        data: baseParams,
        success: (res) => {
          if (res.data.error_code === 0) {
            // 获取新评论的ID和完整数据
            let newCommentId = res.data.data?.comment_id;
            let newCommentData = res.data.data?.comment_data;
            if (!newCommentId) return;

            // 优先使用后端返回的完整数据
            let newComment;
            if (newCommentData) {
              // 使用后端返回的数据（包含匿名处理）
              newComment = newCommentData;
              // 确保replies字段存在
              if (!newComment.replies) {
                newComment.replies = [];
              }

              // 处理后端返回数据的图片和头像
              const imageUtil = require('../../../utils/imageUtil.js');
              if (newComment.images && typeof newComment.images === 'string') {
                try {
                  newComment.images = JSON.parse(newComment.images);
                } catch (e) {
                  newComment.images = [];
                }
              }
              if (newComment.images && newComment.images.length > 0) {
                newComment.images = imageUtil.processImageArray(newComment.images);
              }
              if (newComment.face_url) {
                newComment.face_url = imageUtil.processAvatarUrl(newComment.face_url);
              }
            } else {
              // 备用：构造默认数据（通常不会执行到这里）
              console.log('后端未返回完整数据，使用本地构造');
              const imageUtil = require('../../../utils/imageUtil.js');
              const processedImageUrls = imageUrls && imageUrls.length > 0 ?
                imageUtil.processImageArray(imageUrls) : [];
              const processedFaceUrl = imageUtil.processAvatarUrl(getApp().globalData.face_url);

              newComment = {
                id: newCommentId,
                content: this.data.content.trim(),
                user_id: getApp().globalData.user_id,
                username: getApp().globalData.username,
                face_url: processedFaceUrl,
                titlename: wx.getStorageSync('titlename') || '无',
                titlecolor: wx.getStorageSync('titlecolor') || '0',
                images: processedImageUrls,
                is_liked: false,
                total_likes: 0,
                send_timestamp: '刚刚',
                replies: []
              };
            }

            // 更新本地数据
            const updatedComments = [newComment, ...this.data.comment];

            this.setData({
              comment: updatedComments,
              content: '',
              tempImages: [],
              commentSectionVisible: false,
              commentInputFocused: false,
              showEmoji: false,
              showEmojiList: false,
              highlightedCommentId: newCommentId,
              hasScrolled: false,
              isReply: false,
              replyToUserId: '',
              replyToUsername: '',
              replyType: 'comment',
              replyToPostId: null,
              post_id: '',
              commentPlaceholder: '说点什么...',
              isSubmitting: false // 重置发送状态
            }, () => {
              // 延时滚动到新评论位置
              setTimeout(() => {
                this.scrollToNewComment(newCommentId);
              }, 100);
            });

          } else {
            wx.showToast({
              title: res.data.msg || '评论失败',
              icon: 'none',
              duration: 2000
            });
            // 重置发送状态
            this.setData({
              isSubmitting: false
            });
          }
        },
        fail: (err) => {
          console.error('评论发布失败:', err);
          wx.showToast({
            title: '网络错误',
            icon: 'none',
            duration: 2000
          });
          // 重置发送状态
          this.setData({
            isSubmitting: false
          });
        }
      });
    }
  },

  handleModalClose() {
    // 先触发关闭动画
    this.setData({ showModal: false });
  },

  // 复制联系方式
  copyText(e) {
    const text = e.currentTarget.dataset.text;
    const type = e.currentTarget.dataset.type;

    if (!text) {
      wx.showToast({
        title: '复制内容为空',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: `${type}已复制`,
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 阻止触摸穿透
  preventTouchMove() {
    return false;
  },

  // 阻止点击穿透
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 确保有消息ID、没有正在加载、且还有更多数据时才触发加载
    if (this.data.message && this.data.message.id && !this.data.isLoading && this.data.hasMoreComments) {
      // 防抖处理，避免频繁触发
      if (this.loadingTimeout) {
        clearTimeout(this.loadingTimeout);
      }

      this.loadingTimeout = setTimeout(() => {
        // 保存当前的展开状态
        const currentExpandedComments = {...this.data.expandedComments};

        // 加载更多评论
        this.getCommentList(this.data.message.id, (success) => {
          if (success) {
            // 确保展开状态不会因为加载更多而被重置
            this.setData({
              expandedComments: currentExpandedComments
            });
          }
        });
      }, 300); // 300ms 防抖
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.getShareTitle(),
      path: this.getSharePath(),
      imageUrl: this.getShareImageUrl(),
      success: function(res) {
        wx.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function(res) {
        wx.showToast({
          title: '分享失败',
          icon: 'none',
          duration: 2000
        });
      }
    };
  },

  // 获取分享标题
  getShareTitle() {
    const message = this.data.message;
    if (!message) return '分享';
    
    let title = message.content;
    // 如果内容超过50个字符，截取前50个字符并加上省略号
    if (title && title.length > 50) {
      title = title.substring(0, 50) + '...';
    }
    return title;
  },

  // 获取分享路径
  getSharePath() {
    const message = this.data.message;
    if (!message) return '/pages/fold1/home/<USER>';
    return `/packageEmoji/pages/messageDetail/messageDetail?id=${message.id}`;
  },

  // 获取分享图片
  getShareImageUrl() {
    const message = this.data.message;
    if (!message || !message.images || message.images.length === 0) return '';
    return message.images[0];
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    const message = this.data.message;
    if (!message) {
      return {
        title: '分享',
        query: ''
      };
    }
    
    let title = message.content;
    // 如果内容超过50个字符，截取前50个字符并加上省略号
    if (title && title.length > 50) {
      title = title.substring(0, 50) + '...';
    }
    
    return {
      title: title,
      query: `id=${message.id}`,
      imageUrl: message.images && message.images.length > 0 ? message.images[0] : ''
    };
  },

  // 删除帖子
  deleteMessage() {
    const token = wx.getStorageSync('access_token');
    const userId = wx.getStorageSync('user_id');
    const messageId = this.data.message.id;

    // 参数验证
    if (!token || !messageId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/message/softDelete',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        messageId: messageId
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.error_code === 0) {
          wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
          });
          
          // 发送删除消息事件
          eventBus.emit('messageDeleted', {
            messageId: this.data.message.id
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 添加处理翻转的函数
  handleFlip() {
    this.setData({
      isFlipped: !this.data.isFlipped
    });
  },

  // 滚动到新发表的评论
  scrollToNewComment(commentId) {
    if (!commentId) return;
    
    wx.createSelectorQuery()
      .select(`#comment-${commentId}`)
      .boundingClientRect(rect => {
        if (rect) {
          wx.pageScrollTo({
            scrollTop: rect.top,
            duration: 300
          });
          
          // 添加延时清除高亮效果
          setTimeout(() => {
            this.setData({
              highlightedCommentId: null
            });
          }, 1000);
        }
      })
      .exec();
  },

  // 滚动到指定评论
  scrollToComment() {
    if (this.data.targetCommentId && !this.data.hasScrolled) {
      setTimeout(() => {
        const query = wx.createSelectorQuery();
        query.select(`#comment-${this.data.targetCommentId}`).boundingClientRect();
        query.selectViewport().boundingClientRect();
        query.exec(res => {
          if (res[0] && res[1]) {
            const commentRect = res[0];
            const viewportRect = res[1];
            // 调整偏移量，确保评论在屏幕中间位置
            const scrollTop = commentRect.top - viewportRect.top - (viewportRect.height / 3);
            
            // 设置正在高亮标记
            this.setData({
              isHighlighting: true
            });
            
            wx.pageScrollTo({
              scrollTop: scrollTop,
              duration: 300,
              success: () => {
                this.setData({
                  hasScrolled: true,
                  highlightedCommentId: this.data.targetCommentId
                });
                
                // 3秒后取消高亮，但不改变滚动位置
                setTimeout(() => {
                  // 获取当前滚动位置
                  wx.createSelectorQuery().selectViewport().scrollOffset(res => {
                    const currentScrollTop = res.scrollTop;
                    
                    // 取消高亮状态
                    this.setData({
                      highlightedCommentId: null,
                      isHighlighting: false
                    });
                    
                    // 恢复滚动位置
                    wx.pageScrollTo({
                      scrollTop: currentScrollTop,
                      duration: 0 // 立即滚动，无动画
                    });
                  }).exec();
                }, 3000);
              }
            });
          } else {
            console.log('未找到评论元素，targetCommentId:', this.data.targetCommentId);
          }
        });
      }, 500);  // 增加延时确保DOM完全更新
    }
  },

  // 滚动到指定回复
  scrollToReply() {
    if (this.data.targetReplyId && !this.data.hasScrolled) {
      // 首先确认在页面中是否存在该回复
      let replyExists = false;
      let parentCommentId = null;
      
      // 检查评论列表中是否存在目标回复
      this.data.comment.forEach(comment => {
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.forEach(reply => {
            if (reply.id === this.data.targetReplyId) {
              replyExists = true;
              parentCommentId = comment.id;
            }
          });
        }
      });
      
      if (!replyExists) {
        console.error('目标回复ID不存在于当前评论列表中:', this.data.targetReplyId);
        return;
      }
      
      // 确保父评论的回复是展开的，但保持原有的展开状态
      if (parentCommentId) {
        const parentComment = this.data.comment.find(item => item.id === parentCommentId);
        if (parentComment && parentComment.replies) {
          // 不再强制展开所有回复，而是保持原有展开状态
          // 仅当新回复不在可见范围内时才调整展开状态
          const expandedComments = {...this.data.expandedComments};
          const currentExpanded = expandedComments[parentCommentId] !== undefined ? 
            expandedComments[parentCommentId] : 0;
            
          // 找到新回复的索引
          const replyIndex = parentComment.replies.findIndex(reply => reply.id === this.data.targetReplyId);
          
          // 如果新回复在首位（我们通常已经将其移到首位），只需确保至少显示一条回复
          if (replyIndex === 0 && currentExpanded === 0) {
            // 不需要调整，默认会显示第一条
          } 
          // 如果新回复不在可见范围内，则调整展开数量使其可见
          else if (replyIndex > currentExpanded) {
            // 只展开到该回复的位置
            expandedComments[parentCommentId] = replyIndex + 1;
          }
          
          this.setData({
            expandedComments: expandedComments
          });
        }
      }
      
      // 给页面时间进行渲染
      setTimeout(() => {
        const query = wx.createSelectorQuery();
        query.select(`#reply-${this.data.targetReplyId}`).boundingClientRect();
        query.selectViewport().boundingClientRect();
        query.exec(res => {
          if (res[0] && res[1]) {
            const replyRect = res[0];
            const viewportRect = res[1];
            // 调整偏移量，确保回复在屏幕中间位置
            const scrollTop = replyRect.top - viewportRect.top - (viewportRect.height / 3);
            
            // 设置正在高亮标记
            this.setData({
              isHighlighting: true
            });
            
            wx.pageScrollTo({
              scrollTop: scrollTop,
              duration: 300,
              success: () => {
                this.setData({
                  hasScrolled: true,
                  highlightedReplyId: this.data.targetReplyId
                });
                
                // 3秒后取消高亮，但不改变滚动位置
                setTimeout(() => {
                  // 获取当前滚动位置
                  wx.createSelectorQuery().selectViewport().scrollOffset(res => {
                    const currentScrollTop = res.scrollTop;
                    
                    // 取消高亮状态
                    this.setData({
                      highlightedReplyId: null,
                      isHighlighting: false
                    });
                    
                    // 恢复滚动位置
                    wx.pageScrollTo({
                      scrollTop: currentScrollTop,
                      duration: 0 // 立即滚动，无动画
                    });
                  }).exec();
                }, 3000);
              }
            });
          } else {
            console.error('未找到回复元素，targetReplyId:', this.data.targetReplyId);
            // 元素不存在，查看是否有父评论ID
            if (parentCommentId) {
              // 滚动到父评论位置
              const parentQuery = wx.createSelectorQuery();
              parentQuery.select(`#comment-${parentCommentId}`).boundingClientRect();
              parentQuery.selectViewport().boundingClientRect();
              parentQuery.exec(parentRes => {
                if (parentRes[0] && parentRes[1]) {
                  const commentRect = parentRes[0];
                  const viewportRect = parentRes[1];
                  const scrollTop = commentRect.top - viewportRect.top - (viewportRect.height / 3);
                  
                  wx.pageScrollTo({
                    scrollTop: scrollTop,
                    duration: 300
                  });
                }
              });
            }
          }
        });
      }, 800);  // 增加延时确保DOM完全更新
    }
  },

  // 检查引导状态
  checkGuideStatus() {
    const user_id = wx.getStorageSync('user_id');
    
    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/getHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'message_page'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data && typeof res.data === 'object') {
          if (res.data.code === 1) {
            if (res.data.data && typeof res.data.data === 'object' && res.data.data.should_show_hint) {
              this.setData({
                showGuideHint: true,
                guideStep: 1,
                guideClosing: false
              });
            }
          }
        }
      }
    });
  },

  // 下一步引导
  nextGuideStep() {
    const nextStep = this.data.guideStep + 1;
    this.setData({
      guideStep: nextStep
    });
  },

  // 关闭引导提示
  closeGuideHint() {
    const user_id = wx.getStorageSync('user_id');
    
    this.setData({
      guideClosing: true
    });

    wx.request({
      url: getApp().globalData.wangz + '/guide_hint/updateHintStatus',
      method: 'POST',
      data: {
        user_id: user_id,
        page_key: 'message_page',
        step_key: 'guide'
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.data && typeof res.data === 'object') {
          if (res.data.code === 1) {
            this.setData({
              showGuideHint: false,
              guideClosing: false
            });
          } else {
            this.setData({
              showGuideHint: false,
              guideClosing: false
            });
          }
        } else {
          this.setData({
            showGuideHint: false,
            guideClosing: false
          });
        }
      },
      fail: () => {
        this.setData({
          showGuideHint: false,
          guideClosing: false
        });
      }
    });
  },

  skipGuide() {
    this.closeGuideHint();
  },

  // 显示评论操作菜单
  showCommentActionSheet: function(e) {
    const comment = e.currentTarget.dataset.comment;
    const content = comment.content;
    const commentId = comment.id;
    const isCurrentUser = comment.user_id == this.data.userInfo.id;
    
    // 菜单项列表
    let itemList = ['复制', '+1'];
    // 如果是当前用户的评论，添加删除选项
    if (isCurrentUser) {
      itemList.push('删除');
    }
    
    wx.showActionSheet({
      itemList: itemList,
      itemColor: '#000000',
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 复制
            wx.setClipboardData({
              data: content,
              success: () => {
                wx.showToast({
                  title: '复制成功',
                  icon: 'success'
                });
              }
            });
            break;
          case 1: // +1
            if (comment.images && comment.images.length > 0) {
              wx.showToast({
                title: '无法复制带图片的评论',
                icon: 'none'
              });
            } else {
              this.setData({
                content: content,
                isReply: false,
                commentInputFocused: false,
                commentSectionVisible: false
              });
              this.submitCommentWithImages([]);
              wx.showToast({
                title: '+1成功',
                icon: 'success'
              });
            }
            break;
          case 2: // 删除（仅当是当前用户的评论时）
            if (isCurrentUser) {
              wx.showModal({
                title: '确认删除',
                content: '确定要删除这条评论吗？',
                confirmColor: '#FF4D4F',
                success: (res) => {
                  if (res.confirm) {
                    this.deleteComment(commentId);
                  }
                }
              });
            }
            break;
        }
      }
    });
  },

  showReplyActionSheet: function(e) {
    const reply = e.currentTarget.dataset.reply;
    const commentId = e.currentTarget.dataset.commentId;
    
    // 添加长按反馈效果
    const replyId = reply.id;
    this.setData({
      [`longPressReplyId`]: replyId
    });
    
    // 在操作菜单显示后移除长按效果
    setTimeout(() => {
      this.setData({
        [`longPressReplyId`]: null
      });
    }, 200);
    
    const isCurrentUser = reply.user_id == this.data.userInfo.id;
    const isAdmin = this.data.userInfo.is_admin;
    
    let itemList = ['复制'];
    if (!reply.images || reply.images.length === 0) {
      itemList.push('+1');
    }
    if (isCurrentUser || isAdmin) {
      itemList.push('删除');
    }

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 复制
            wx.setClipboardData({
              data: reply.content,
              success: () => {
                wx.showToast({
                  title: '内容已复制',
                  icon: 'success'
                });
              }
            });
            break;
          case 1:
            // 如果没有图片，第二个选项是+1
            if (!reply.images || reply.images.length === 0) {
              // 使用原回复内容进行+1
              this.quickReply(commentId, reply.content, reply.id);
            } 
            // 如果有图片且用户可以删除，第二个选项是删除
            else if (isCurrentUser || isAdmin) {
              this.handleReplyDelete(commentId, replyId);
            }
            break;
          case 2: // 删除（只有当第二个选项是+1时才会有第三个选项，且必定是删除）
            if (!reply.images || reply.images.length === 0) {
              if (isCurrentUser || isAdmin) {
                this.handleReplyDelete(commentId, replyId);
              }
            }
            break;
        }
      }
    });
  },
  
  // 对评论进行+1，发布为新评论而不是回复
  plusOneComment(content) {
    // 获取用户信息
    const userId = wx.getStorageSync('user_id');
    const username = wx.getStorageSync('username') || '';
    const face_url = wx.getStorageSync('face_url') || '';
    const titlename = wx.getStorageSync('titlename') || '无';
    const titlecolor = wx.getStorageSync('titlecolor') || '0';
    
    if (!userId || !this.data.message || !this.data.message.id) {
      wx.showToast({
        title: '用户信息不完整',
        icon: 'none'
      });
      return;
    }
    
    // 准备参数
    const params = {
      user_id: userId,
      username: username,
      message_id: this.data.message.id,
      content: content,
      face_url: face_url,
      images: JSON.stringify([]),
      titlename: titlename,
      titlecolor: titlecolor
    };
    
    // 显示加载提示
    wx.showLoading({
      title: '发送中...',
      mask: true
    });
    
    // Token验证
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.hideLoading()
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 发送请求
    wx.request({
      url: getApp().globalData.wangz + '/comment/publishNewComment',
      method: "POST",
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: params,
      success: (res) => {
        wx.hideLoading();
        
        if (res.data.error_code === 0) {
          // 获取新评论的ID和完整数据
          let newCommentId = res.data.data?.comment_id;
          let newCommentData = res.data.data?.comment_data;
          if (!newCommentId) {
            wx.showToast({
              title: '+1成功',
              icon: 'success'
            });
            return;
          }

          // 优先使用后端返回的完整数据
          let newComment;
          if (newCommentData) {
            // 使用后端返回的数据（包含匿名处理）
            newComment = newCommentData;
            // 确保replies字段存在
            if (!newComment.replies) {
              newComment.replies = [];
            }

            // 处理后端返回数据的图片和头像
            const imageUtil = require('../../../utils/imageUtil.js');
            if (newComment.images && typeof newComment.images === 'string') {
              try {
                newComment.images = JSON.parse(newComment.images);
              } catch (e) {
                newComment.images = [];
              }
            }
            if (newComment.images && newComment.images.length > 0) {
              newComment.images = imageUtil.processImageArray(newComment.images);
            }
            if (newComment.face_url) {
              newComment.face_url = imageUtil.processAvatarUrl(newComment.face_url);
            }
          } else {
            // 备用：构造默认数据（通常不会执行到这里）
            console.log('后端未返回完整数据，使用本地构造');
            const imageUtil = require('../../../utils/imageUtil.js');
            const processedFaceUrl = imageUtil.processAvatarUrl(face_url);

            newComment = {
              id: newCommentId,
              content: content,
              user_id: userId,
              username: username,
              face_url: processedFaceUrl,
              titlename: titlename,
              titlecolor: titlecolor,
              images: [],
              is_liked: false,
              total_likes: 0,
              send_timestamp: '刚刚',
              replies: []
            };
          }

          // 更新本地数据
          const updatedComments = [newComment, ...this.data.comment];

          this.setData({
            comment: updatedComments,
            highlightedCommentId: newCommentId,
            hasScrolled: false
          }, () => {
            // 延时滚动到新评论位置
            setTimeout(() => {
              this.scrollToNewComment(newCommentId);
            }, 100);
          });

          wx.showToast({
            title: '+1成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.msg || '+1失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 删除评论
  deleteComment: function(commentId) {
    const token = wx.getStorageSync('access_token');
    const userInfo = this.data.userInfo;
    
    wx.request({
      url: getApp().globalData.wangz + '/comment/softDeleteComment',
      method: 'POST',
      data: {
        comment_id: commentId,
        user_id: userInfo.id,
        token: token
      },
      success: (res) => {
        if (res.data.error_code === 0) {
          // 从本地评论列表中移除被删除的评论
          const updatedComments = this.data.comment.filter(item => item.id !== commentId);
          
          this.setData({
            comment: updatedComments
          }, () => {
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          });
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 删除回复
  deleteReply: function(replyId, commentId) {
    const token = wx.getStorageSync('access_token');
    const userInfo = this.data.userInfo;
    
    wx.request({
      url: getApp().globalData.wangz + '/comment/softDeleteReply',
      method: 'POST',
      data: {
        reply_id: replyId,
        user_id: userInfo.id,
        token: token
      },
      success: (res) => {
        if (res.data.error_code === 0) {
          // 从本地评论列表中移除被删除的回复
          const updatedComments = this.data.comment.map(comment => {
            if (comment.id === commentId) {
              return {
                ...comment,
                replies: comment.replies.filter(reply => reply.id !== replyId)
              };
            }
            return comment;
          });
          
          this.setData({
            comment: updatedComments
          }, () => {
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          });
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 处理投票选项选择
  handleVoteSelect(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    
    // 验证投票是否可用
    if (!this.data.message || !this.data.message.vote) {
      console.error('投票数据不可用');
      return;
    }
    
    // 验证投票是否已结束或用户已投票
    if (this.data.message.vote.has_voted || this.data.message.vote.is_expired) {
      return;
    }
    
    const voteType = this.data.message.vote.vote_type;
    
    // 验证索引的有效性
    const maxIndex = (this.data.message.vote.options || []).length - 1;
    if (isNaN(index) || index < 0 || index > maxIndex) {
      console.error('无效的选项索引:', index);
      wx.showToast({
        title: '无效的选项',
        icon: 'none'
      });
      return;
    }
    
    if (voteType === 'single') {
      // 单选模式：直接提交
      this.submitVote([index]);
    } else {
      // 多选模式：更新选择状态
      const voteSelection = { ...this.data.voteSelection };
      voteSelection[index] = !voteSelection[index];
      
      // 添加选中/取消选中的动画效果
      const currentOptions = [...this.data.message.vote.options];
      currentOptions[index] = {
        ...currentOptions[index],
        animating: true
      };
      
      this.setData({
        voteSelection,
        'message.vote.options': currentOptions,
        hasVoteSelection: Object.values(voteSelection).some(v => v)
      });

      // 移除动画类
      setTimeout(() => {
        currentOptions[index].animating = false;
        this.setData({
          'message.vote.options': currentOptions
        });
      }, 300);
    }
  },

  // 提交投票
  submitVote(selectedIndexes) {
    // 如果是多选且没有传入选项，从 voteSelection 中获取选中的选项
    if (!Array.isArray(selectedIndexes)) {
      selectedIndexes = Object.entries(this.data.voteSelection)
        .filter(([_, selected]) => selected)
        .map(([index]) => parseInt(index))
        .filter(index => !isNaN(index));
    }

    // 验证是否有选择
    if (selectedIndexes.length === 0) {
      return wx.showToast({
        title: '请选择投票选项',
        icon: 'none'
      });
    }

    // 验证选项索引的有效性
    const maxIndex = (this.data.message.vote.options || []).length - 1;
    const invalidIndexes = selectedIndexes.filter(index => 
      typeof index !== 'number' || isNaN(index) || index < 0 || index > maxIndex
    );
    
    if (invalidIndexes.length > 0) {
      return wx.showToast({
        title: '无效的选项',
        icon: 'none'
      });
    }

    // 验证单选时是否只选择了一个选项
    if (this.data.message.vote.vote_type === 'single' && selectedIndexes.length > 1) {
      return wx.showToast({
        title: '单选只能选择一个选项',
        icon: 'none'
      });
    }

    // 准备请求参数
    const userId = wx.getStorageSync('user_id');
    
    // 根据投票类型处理参数，单选传整数，多选传数组形式
    let params = {};
    
    if (this.data.message.vote.vote_type === 'single') {
      // 单选投票，直接传递选项索引
      params = {
        vote_id: this.data.message.vote.id,
        user_id: userId,
        option_indexes: selectedIndexes[0]  // 单选只取第一个索引
      };
    } else {
      // 多选投票，使用option_indexes[0]=0&option_indexes[1]=1的格式
      params = {
        vote_id: this.data.message.vote.id,
        user_id: userId
      };
      
      // 将多个选项添加为单独的参数
      selectedIndexes.forEach((index, i) => {
        params[`option_indexes[${i}]`] = index;
      });
    }

    // 调用投票接口
    wx.request({
      url: `${app.globalData.wangz}/message/submitVote`,
      method: 'POST',
      data: params,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token')
      },
      success: (res) => {
        if (res.data.error_code === 0) {
          // 构建用户选择
          const userChoices = {};
          selectedIndexes.forEach(index => {
            userChoices[index] = true;
          });
          
          // 更新投票状态和统计数据
          const currentResults = res.data.data.current_results;
          const totalVotes = currentResults.total_voters;
          const optionsCount = currentResults.options_count;
          
          // 先更新选中状态
          this.setData({
            'message.vote.has_voted': true,
            'message.vote.user_choices': userChoices,
            voteSelection: {},
            hasVoteSelection: false
          });

          // 延迟更新进度条，让用户看到动画效果
          setTimeout(() => {
            // 更新选项的进度条宽度
            const updatedOptions = this.data.message.vote.options.map((option, index) => {
              const count = optionsCount[index] || 0;
              const progressWidth = totalVotes > 0 ? (count / totalVotes * 100) : 0;
              return {
                ...option,
                progressWidth
              };
            });
            
            this.setData({
              'message.vote.options': updatedOptions,
              'message.vote.statistics': {
                total_voters: totalVotes,
                options_count: optionsCount
              }
            });
          }, 100);
          
        } else {
          let errorMsg = '投票失败';
          switch (res.data.error_code) {
            case 2:
              errorMsg = res.data.msg || '投票参数错误';
              break;
            default:
              errorMsg = res.data.msg || '投票失败';
          }
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('投票请求失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取投票详情
  getVoteDetails(voteId) {
    const userId = wx.getStorageSync('user_id');
    wx.request({
      url: `${app.globalData.wangz}/message/getVoteDetails`,
      method: 'POST',
      data: {
        vote_id: voteId,
        user_id: userId
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token')
      },
      success: (res) => {
        if (res.data.error_code === 0) {
          const voteData = res.data.data;
          const voteInfo = voteData.vote_info;
          const statistics = voteData.statistics;
          
          // 计算进度条
          const totalVotes = statistics.total_voters;
          const options = voteInfo.options.map((option, index) => {
            const count = statistics.options_count[index] || 0;
            const progressWidth = totalVotes > 0 ? (count / totalVotes * 100) : 0;
            return {
              text: option,
              progressWidth
            };
          });

          // 更新投票数据
          this.setData({
            'message.vote.options': options,
            'message.vote.title': voteInfo.title,
            'message.vote.type': voteInfo.type,
            'message.vote.deadline': voteInfo.deadline,
            'message.vote.is_expired': voteInfo.is_expired,
            'message.vote.has_voted': voteInfo.has_voted,
            'message.vote.user_choices': voteInfo.user_choices,
            'message.vote.statistics': statistics
          });
        }
      }
    });
  },

  // 手动更新投票统计
  updateVoteStatistics(selectedIndexes) {
    // 获取现有统计数据的副本
    const statistics = JSON.parse(JSON.stringify(this.data.message.vote.statistics || {
      total_voters: 0,
      options_count: Array(this.data.message.vote.options.length).fill(0)
    }));
    
    // 增加总投票人数
    statistics.total_voters = (statistics.total_voters || 0) + 1;
    
    // 增加选中选项的计数
    selectedIndexes.forEach(index => {
      statistics.options_count[index] = (statistics.options_count[index] || 0) + 1;
    });
    
    // 更新统计数据
    this.setData({
      'message.vote.statistics': statistics
    });
    
    // 计算并更新进度条
    setTimeout(() => {
      const totalVotes = statistics.total_voters;
      
      // 获取现有的选项数组
      const currentOptions = [...this.data.message.vote.options];
      
      // 更新每个选项的进度条宽度
      const updatedOptions = currentOptions.map((option, index) => {
        const count = statistics.options_count[index] || 0;
        const progressWidth = totalVotes > 0 ? (count / totalVotes * 100) : 0;
        return {
          ...option,
          progressWidth
        };
      });
      
      this.setData({
        'message.vote.options': updatedOptions
      });
    }, 50);
  },

  // 格式化日期时间
  formatDateTime(date) {
    const dt = new Date(date);
    const year = dt.getFullYear();
    const month = String(dt.getMonth() + 1).padStart(2, '0');
    const day = String(dt.getDate()).padStart(2, '0');
    const hour = String(dt.getHours()).padStart(2, '0');
    const minute = String(dt.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 展开或收起评论的回复
  toggleReplies(e) {
    const commentId = e.currentTarget.dataset.id;
    const comment = this.data.comment.find(item => item.id === commentId);

    if (!comment || !comment.replies || comment.replies.length <= this.data.defaultRepliesShow) {
      return; // 没有足够的回复需要展开
    }

    const expandedComments = { ...this.data.expandedComments };
    const currentExpanded = expandedComments[commentId] !== undefined ?
      expandedComments[commentId] : this.data.defaultRepliesShow;

    // 如果已经全部展开，则收起（设置为默认显示数量）
    if (currentExpanded >= comment.replies.length) {
      expandedComments[commentId] = this.data.defaultRepliesShow;
    } else {
      // 否则增加展开数量，每次增加repliesPerLoad条，但不超过总数
      expandedComments[commentId] = Math.min(
        comment.replies.length,
        currentExpanded + this.data.repliesPerLoad
      );
    }

    this.setData({ expandedComments });
  },
  
  // 获取某个评论当前可见的回复数量
  getVisibleRepliesCount(commentId) {
    const expandedState = this.data.expandedComments[commentId];
    if (expandedState === undefined) {
      return this.data.defaultRepliesShow; // 默认显示数量
    }
    return expandedState;
  },
  
  // 获取某个评论剩余未显示的回复数量
  getRemainingRepliesCount(commentId) {
    const comment = this.data.commentList.find(item => item.id === commentId);
    if (!comment || !comment.replies) {
      return 0;
    }
    
    const visibleCount = this.getVisibleRepliesCount(commentId);
    return Math.max(0, comment.replies.length - visibleCount);
  },

  // 上传图片方法
  uploadImages() {
    return new Promise((resolve, reject) => {
      if (this.data.tempImages.length === 0) {
        resolve([]);
        return;
      }

      // 显示上传中提示
      wx.showLoading({
        title: '上传图片中...',
        mask: true
      });

      const uploadPromises = this.data.tempImages.map(tempFilePath => {
        return new Promise((resolveUpload, rejectUpload) => {
          wx.uploadFile({
            url: getApp().globalData.wangz + '/upload/uploadToCos',  // 统一COS上传接口
            filePath: tempFilePath,
            name: 'file',  // 参数名为file
            header: {
              'token': wx.getStorageSync('access_token')
            },
            formData: {
              type: 'comment'  // 图片类型
            },
            success: (res) => {
              try {
                const data = JSON.parse(res.data);
                if (data.code === 200) {
                  resolveUpload(data.data.key); // 存储相对路径
                } else {
                  console.error('图片上传失败:', data);
                  rejectUpload(new Error(data.msg || '上传失败'));
                }
              } catch (e) {
                console.error('解析响应失败:', e, res.data);
                rejectUpload(new Error('服务器响应格式错误'));
              }
            },
            fail: (error) => {
              console.error('上传请求失败:', error);
              rejectUpload(error);
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        });
      });

      Promise.all(uploadPromises)
        .then(imageUrls => {
          // 过滤掉上传失败的图片
          const validUrls = imageUrls.filter(url => url);
          if (validUrls.length === 0) {
            wx.showToast({
              title: '图片上传失败',
              icon: 'none'
            });
            reject(new Error('所有图片上传失败'));
          } else if (validUrls.length < this.data.tempImages.length) {
            wx.showToast({
              title: '部分图片上传失败',
              icon: 'none'
            });
            resolve(validUrls);
          } else {
            resolve(validUrls);
          }
        })
        .catch(error => {
          console.error('图片上传过程出错:', error);
          wx.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
          reject(error);
        });
    });
  },

  // 显示管理选项
  showManageOptions() {
    const userId = wx.getStorageSync('user_id');
    const isAdmin = roleManager.hasAdminPermission();
    const isAuthor = this.data.message && userId == this.data.message.user_id;
    
    this.setData({
      showCustomActionSheet: true,
      canDelete: isAdmin || isAuthor
    });
  },
  
  // 关闭自定义ActionSheet
  closeCustomActionSheet() {
    this.setData({
      showCustomActionSheet: false
    });
  },
  
  // 处理删除操作
  handleDelete() {
    this.closeCustomActionSheet();
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条帖子吗？',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          this.deleteMessage();
        }
      }
    });
  },
  
  // 触发分享
  triggerShare() {
    // 显示自定义分享面板
    this.setData({
      showSharePanel: true
    });
  },
  
  // 关闭分享面板
  closeSharePanel() {
    this.setData({
      showSharePanel: false
    });
  },

  // 选择图片
  chooseImage() {
    const remainingCount = 3 - this.data.tempImages.length;
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能添加3张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remainingCount,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.setData({
          tempImages: [...this.data.tempImages, ...tempFilePaths]
        });
      }
    });
  },

  // 打开相机
  openCamera() {
    const remainingCount = 3 - this.data.tempImages.length;
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能添加3张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remainingCount,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.setData({
          tempImages: [...this.data.tempImages, ...tempFilePaths]
        });
      }
    });
  },

  // 移除图片
  removeImage(e) {
    const index = e.currentTarget.dataset.index;
    const tempImages = this.data.tempImages;
    tempImages.splice(index, 1);
    this.setData({
      tempImages: tempImages
    });
  },

  // 预览图片
  previewImages(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.tempImages[index],
      urls: this.data.tempImages
    });
  },

  // 切换表情面板
  toggleEmoji() {
    if (this.data.showEmoji) {
      this.setData({
        showEmoji: false,
        commentInputFocused: true
      });
    } else {
      this.setData({
        showEmoji: true,
        showEmojiList: false,
        commentInputFocused: false,
        keyboardHeight: 0
      });
    }
  },

  // 切换emoji表情列表
  toggleEmojiList() {
    if (this.data.showEmojiList) {
      this.setData({
        showEmojiList: false,
        commentInputFocused: true
      });
    } else {
      this.setData({
        showEmojiList: true,
        showEmoji: false,
        commentInputFocused: false,
        keyboardHeight: 0
      });
    }
  },

  // 选择表情
  selectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji;
    const content = this.data.content + emoji;
    this.setData({
      content: content
    });
  },

  // 选择emoji文本表情
  selectEmojiText(e) {
    const emoji = e.currentTarget.dataset.emoji;
    const content = this.data.content + emoji;
    this.setData({
      content: content
    });
  },

  // 空函数，用于阻止事件冒泡
  skipEvent() {
    // 不做任何事情，仅用于阻止事件冒泡
  },

  // 快速回复（+1功能）
  quickReply(commentId, content, replyId) {
    // 获取用户信息
    const userId = wx.getStorageSync('user_id');
    const username = wx.getStorageSync('username') || '';
    const face_url = wx.getStorageSync('face_url') || '';
    const titlename = wx.getStorageSync('titlename') || '无';
    const titlecolor = wx.getStorageSync('titlecolor') || '0';

    if (!userId || !this.data.message || !this.data.message.id) {
      wx.showToast({
        title: '用户信息不完整',
        icon: 'none'
      });
      return;
    }

    // Token验证
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 准备请求参数
    let params = {
      user_id: userId,
      username: username,
      message_id: this.data.message.id,
      content: content,
      face_url: face_url,
      titlename: titlename,
      titlecolor: titlecolor,
      images: JSON.stringify([])
    };

    // 如果是回复评论
    if (commentId && !replyId) {
      params.parent_comment_id = commentId;
      params.reply_type = 'comment';

      // 获取原评论的用户信息
      const comment = this.data.comment.find(item => item.id === commentId);
      if (comment) {
        params.reply_to_user_id = comment.user_id;
        params.reply_to_username = comment.username;
      } else {
        wx.showToast({
          title: '评论数据错误',
          icon: 'none'
        });
        return;
      }
    }
    // 如果是回复另一条回复（楼中楼+1）
    else if (commentId && replyId) {
      params.parent_comment_id = commentId;
      params.reply_type = 'reply';

      // 获取原回复的信息
      let targetReply = null;
      const parentComment = this.data.comment.find(item => item.id === commentId);
      if (parentComment && parentComment.replies) {
        targetReply = parentComment.replies.find(item => item.id === replyId);
      }

      if (targetReply) {
        // +1回复应该与被+1的回复有相同的回复目标
        // 如果被+1的回复是回复评论的，那么+1也回复评论
        // 如果被+1的回复是回复另一个回复的，那么+1也回复同一个目标
        if (targetReply.reply_type === 'comment') {
          // 被+1的回复是回复评论的，+1也回复同一个评论
          params.reply_to_user_id = parentComment.user_id;
          params.reply_to_username = parentComment.username;
          params.reply_to_post_id = null;
        } else if (targetReply.reply_type === 'reply' && targetReply.reply_to_post_id) {
          // 被+1的回复是回复另一个回复的，+1也回复同一个目标
          params.reply_to_user_id = targetReply.reply_to_user_id;
          params.reply_to_username = targetReply.reply_to_username;
          params.reply_to_post_id = targetReply.reply_to_post_id;
        } else {
          // 默认回复评论作者
          params.reply_to_user_id = parentComment.user_id;
          params.reply_to_username = parentComment.username;
          params.reply_to_post_id = null;
        }
      } else {
        wx.showToast({
          title: '回复数据错误',
          icon: 'none'
        });
        return;
      }
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 发送请求
    wx.request({
      url: `${app.globalData.wangz}/comment/publishNewPost`,
      method: 'POST',
      data: params,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      success: (res) => {
        wx.hideLoading();

        if (res.data.error_code === 0) {
          // 获取新回复的ID和完整数据
          let newReplyId = res.data.data?.reply_id;
          let newReplyData = res.data.data?.reply_data;
          if (!newReplyId) {
            wx.showToast({
              title: '+1成功',
              icon: 'success'
            });
            return;
          }

          // 优先使用后端返回的完整数据
          let newReply;
          if (newReplyData) {
            // 使用后端返回的数据（包含匿名处理）
            newReply = newReplyData;

            // 处理后端返回数据的图片和头像
            const imageUtil = require('../../../utils/imageUtil.js');
            if (newReply.images && typeof newReply.images === 'string') {
              try {
                newReply.images = JSON.parse(newReply.images);
              } catch (e) {
                newReply.images = [];
              }
            }
            if (newReply.images && newReply.images.length > 0) {
              newReply.images = imageUtil.processImageArray(newReply.images);
            }
            if (newReply.face_url) {
              newReply.face_url = imageUtil.processAvatarUrl(newReply.face_url);
            }
          } else {
            // 备用：构造默认数据（通常不会执行到这里）
            console.log('后端未返回完整回复数据，使用本地构造');
            const imageUtil = require('../../../utils/imageUtil.js');
            const processedFaceUrl = imageUtil.processAvatarUrl(face_url);

            newReply = {
              id: newReplyId,
              content: content,
              user_id: userId,
              username: username,
              face_url: processedFaceUrl,
              titlename: titlename,
              titlecolor: titlecolor,
              reply_type: params.reply_type,
              reply_to_user_id: params.reply_to_user_id,
              reply_to_username: params.reply_to_username,
              reply_to_post_id: params.reply_to_post_id || null,
              images: [],
              is_liked: false,
              total_likes: 0,
              send_timestamp: '刚刚'
            };
          }

          // 更新界面数据
          const updatedComments = this.data.comment.map(comment => {
            if (comment.id === commentId) {
              // 将新回复添加到回复列表的开头
              const updatedReplies = [newReply, ...(comment.replies || [])];
              return {
                ...comment,
                replies: updatedReplies
              };
            }
            return comment;
          });

          // 确保新回复可见，但保持现有展开状态
          const expandedComments = {...this.data.expandedComments};
          const targetComment = updatedComments.find(comment => comment.id === commentId);

          // 如果该评论的回复数量超过默认显示数量，需要设置展开状态
          if (targetComment && targetComment.replies && targetComment.replies.length > this.data.defaultRepliesShow) {
            // 如果之前没有展开状态，设置为默认显示数量
            if (expandedComments[commentId] === undefined) {
              expandedComments[commentId] = this.data.defaultRepliesShow;
            }
            // 如果新回复不在可见范围内，确保至少显示到新回复
            if (expandedComments[commentId] < 1) {
              expandedComments[commentId] = Math.max(1, this.data.defaultRepliesShow);
            }
          }

          this.setData({
            comment: updatedComments,
            expandedComments: expandedComments
          });

          wx.showToast({
            title: '+1成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.msg || '+1失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },
  
  // 处理评论删除
  handleCommentDelete(commentId) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条评论吗？',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          this.deleteComment(commentId);
        }
      }
    });
  },
  
  // 处理回复删除
  handleReplyDelete(commentId, replyId) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条回复吗？',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          this.deleteReply(replyId, commentId);
        }
      }
    });
  },

  // 处理评论长按事件
  showCommentActionSheet(e) {
    const comment = e.currentTarget.dataset.comment;
    // 添加长按反馈效果
    const commentId = comment.id;
    this.setData({
      [`longPressCommentId`]: commentId
    });
    
    // 在操作菜单显示后移除长按效果
    setTimeout(() => {
      this.setData({
        [`longPressCommentId`]: null
      });
    }, 200);
    
    // 检查是否是当前用户或管理员
    const isCurrentUser = comment.user_id == this.data.userInfo.id;
    const isAdmin = this.data.userInfo.is_admin;
    
    // 创建菜单选项
    let itemList = ['复制'];
    if (!comment.images || comment.images.length === 0) {
      itemList.push('+1'); // 如果没有图片，才能快速加1
    }
    if (isCurrentUser || isAdmin) {
      itemList.push('删除');
    }
    
    // 显示操作菜单
    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 复制评论内容
            wx.setClipboardData({
              data: comment.content,
              success: () => {
                wx.showToast({
                  title: '内容已复制',
                  icon: 'success'
                });
              }
            });
            break;
          case 1:
            // 如果没有图片，第二个选项是+1
            if (!comment.images || comment.images.length === 0) {
              // 直接使用现有评论接口发送+1评论，而不是回复
              this.setData({
                content: comment.content,
                isReply: false
              });
              this.submitCommentWithImages([]);
              wx.showToast({
                title: '+1发送中',
                icon: 'loading'
              });
            } 
            // 如果有图片且用户可以删除，第二个选项是删除
            else if (isCurrentUser || isAdmin) {
              this.handleCommentDelete(comment.id);
            }
            break;
          case 2: // 删除（只有当第二个选项是+1时才会有第三个选项，且必定是删除）
            if (!comment.images || comment.images.length === 0) {
              if (isCurrentUser || isAdmin) {
                this.handleCommentDelete(comment.id);
              }
            }
            break;
        }
      }
    });
  },

  // 回复按钮点击事件
  handleReplyClick() {
    // 检查用户是否有权限回复
    if (roleManager.hasBasePermission()) {
      // 已认证用户，可以回复
      this.setData({
        showReplyInput: true,
        focusReplyInput: true
      });
    } else {
      // 未认证用户，提示认证
      wx.showToast({
        title: '只有本校生可以回复，请先完成学生认证',
        icon: 'none',
        duration: 2000
      });
    }
  },
  
  // 检查管理员权限
  checkAdminPermission() {
    const isAdmin = roleManager.hasAdminPermission();
    this.setData({ isAdmin });
    return isAdmin;
  },

  // 处理举报操作
  handleReport() {
    this.closeCustomActionSheet();
    const reportComponent = this.selectComponent('#reportComponent');
    if (reportComponent) {
      reportComponent.showReportDialog(this.data.message.id);
    }
  },

  /**
   * 帖子点赞状态变化处理（新的统一点赞组件）
   */
  onMessageLikeChange: function(e) {
    const { isLiked, totalLikes } = e.detail

    // 更新帖子点赞状态
    this.setData({
      'message.is_liked': isLiked,
      'message.total_likes': totalLikes
    })

    // 发送点赞状态更新事件
    const app = getApp()
    if (app.globalData.eventBus) {
      app.globalData.eventBus.emit('likeStatusChanged', {
        messageId: this.data.message.id,
        isLiked: isLiked,
        totalLikes: totalLikes
      })
    }
  }
})