/**
 * 分享配置使用示例
 * 展示如何在页面中使用统一的分享配置
 *
 * 简化版本：
 * - 允许分享的页面：添加 onShareAppMessage 和 onShareTimeline 方法
 * - 不允许分享的页面：删除分享方法即可
 */

const { getShareAppMessageConfig, getShareTimelineConfig, setupPageShare } = require('./shareConfig.js');

/**
 * 方法一：手动配置分享方法（推荐）
 */
const manualShareExample = {
  /**
   * 分享给好友
   */
  onShareAppMessage() {
    return getShareAppMessageConfig({
      pageName: '课程评价', // 页面名称
      // customTitle: '自定义标题' // 可选：自定义标题
    });
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return getShareTimelineConfig({
      pageName: '课程评价',
      // customTitle: '自定义朋友圈标题' // 可选：自定义标题
    });
  }
};

/**
 * 方法二：使用 setupPageShare 快速配置
 */
function quickSetupExample() {
  // 在页面的 onLoad 中调用
  setupPageShare(this, {
    pageName: '实用工具', // 页面名称
    // customTitle: '自定义标题' // 可选
  });
}

/**
 * 完整的页面示例
 */
const completePageExample = {
  data: {
    // 页面数据
  },

  onLoad(options) {
    // 快速设置分享功能
    setupPageShare(this, {
      pageName: '专业介绍'
    });
  },

  // 其他页面方法...
};

/**
 * 禁止分享的页面示例
 * 只需要删除 onShareAppMessage 和 onShareTimeline 方法即可
 */
const noSharePageExample = {
  data: {
    // 页面数据
  },

  onLoad(options) {
    // 不调用任何分享设置方法
  },

  // 没有 onShareAppMessage 和 onShareTimeline 方法
  // 微信会自动禁用分享功能
};

module.exports = {
  manualShareExample,
  quickSetupExample,
  completePageExample,
  noSharePageExample
};
