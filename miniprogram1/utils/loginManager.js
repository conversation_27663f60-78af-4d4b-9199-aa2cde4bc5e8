// 登录管理器类
class LoginManager {
  constructor() {
    this.isLoggingIn = false;
    this.initPromise = null;
    this.retryCount = 0;
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1秒后重试
    this.isVerifyingStatus = false;
  }

  // 初始化方法
  init(app) {
    if (!this.initPromise) {
      this.initPromise = new Promise((resolve, reject) => {
        let retryCount = 0;
        const maxRetries = 3;
        const retryInterval = 100;

        const tryInit = () => {
          if (app) {
            if (app.globalData) {
              this.app = app;
              resolve(app);
            } else {
              if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(tryInit, retryInterval);
              } else {
                reject(new Error('App globalData not available after retries'));
              }
            }
          } else {
            const appInstance = getApp();
            if (appInstance && appInstance.globalData) {
              this.app = appInstance;
              resolve(appInstance);
            } else {
              if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(tryInit, retryInterval);
              } else {
                reject(new Error('Failed to get App instance after retries'));
              }
            }
          }
        };

        tryInit();
      });
    }
    return this.initPromise;
  }

  // 获取app实例的辅助方法
  async _getApp() {
    if (this.app) {
      return this.app;
    }

    await this.init();
    if (!this.app || !this.app.globalData) {
      throw new Error('App instance is not ready');
    }
    return this.app;
  }

  // 用户登录
  async login(isAutoLogin = false) {
    try {
      // 获取应用实例
      const app = await this._getApp();

      console.log('[登录] 开始' + (isAutoLogin ? '自动' : '') + '登录流程');

      // 微信登录获取code
      const wxLoginRes = await this._wxLogin();
      if (!wxLoginRes.code) {
        throw new Error('获取微信登录凭证失败');
      }

      // 服务器登录
      const serverLoginRes = await this._serverLogin(wxLoginRes.code);

      // 处理服务器响应
      if (serverLoginRes.statusCode !== 200) {
        throw new Error(`服务器响应异常: ${serverLoginRes.statusCode}`);
      }

      const { data } = serverLoginRes;

      if (data.error_code !== 0) {
        throw new Error(`登录失败: ${data.msg || '未知错误'}`);
      }

      // 处理登录成功
      await this._handleLoginSuccess(data.data);

      // 登录成功后验证用户状态（但不循环登录）
      await this._verifyUserStatus(data.data.status);

      console.log('[登录] 完成');
      return true;
    } catch (error) {
      console.error('[登录] 失败:', error);

      // 如果不是自动登录，显示错误提示
      if (!isAutoLogin) {
        wx.showToast({
          title: `登录失败: ${error.message}`,
          icon: 'none',
          duration: 2000
        });
      }

      return false;
    }
  }

  // 自动登录
  async autoLogin() {
    const tokenExpire = wx.getStorageSync('token_expire');
    const refreshToken = wx.getStorageSync('refresh_token');
    const now = Date.now();

    if (!tokenExpire || !refreshToken) {
      return this.login(true);
    }

    if (now >= tokenExpire) {
      console.log('[登录] token已过期，尝试刷新');
      return this.refreshToken();
    } else if (now >= tokenExpire - 5 * 60 * 1000) {
      console.log('[登录] token即将过期，提前刷新');
      return this.refreshToken();
    } else {
      console.log('[登录] token有效，恢复用户数据');
      return this.restoreUserData();
    }
  }

  // 刷新token
  async refreshToken() {
    const refreshToken = wx.getStorageSync('refresh_token');
    if (!refreshToken) {
      console.log('[登录] 无refresh_token，尝试重新登录');
      return this.login(true);
    }

    try {
      const app = await this._getApp();
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.wangz + '/user/refreshToken',
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            refresh_token: refreshToken
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.error_code === 0) {
        this._updateToken(res.data.data);
        console.log('[登录] token刷新成功');
        return this.restoreUserData();
      } else {
        console.log('[登录] token刷新失败，尝试重新登录');
        return this.login(true);
      }
    } catch (error) {
      console.log('[登录] token刷新出错，尝试重新登录:', error);
      return this.login(true);
    }
  }

  // 恢复用户数据
  async restoreUserData() {
    const userData = {
      username: wx.getStorageSync('username'),
      id: wx.getStorageSync('user_id'),
      face_url: wx.getStorageSync('face_url'),
      phone: wx.getStorageSync('phone'),
      status: wx.getStorageSync('status'),
      openid: wx.getStorageSync('openid'),
      titlename: wx.getStorageSync('titlename'),
      titlecolor: wx.getStorageSync('titlecolor'),
      unread: wx.getStorageSync('unread') || 0,
      access_token: wx.getStorageSync('access_token'),
      refresh_token: wx.getStorageSync('refresh_token'),
      token: wx.getStorageSync('access_token'),
      status_code: wx.getStorageSync('status_code'),
      school_id: wx.getStorageSync('school_id'), // 恢复school_id
    };

    // 不再设置默认学校，让用户自己选择

    if (userData.id && userData.access_token) {
      await this._saveUserData(userData);

      // 验证用户状态码，如果返回false（需要重新登录）则执行登录
      const verifyResult = await this._verifyUserStatus(userData.status);
      if (verifyResult === false) {
        console.log('[登录] 状态验证要求重新登录');
        return this.login(true);
      }

      return true;
    } else {
      return this.login(true);
    }
  }

  // 新增：验证用户状态
  async _verifyUserStatus(localStatus) {
    try {
      const app = await this._getApp();

      // 从本地存储获取用户ID、access_token和状态码
      const userId = wx.getStorageSync('user_id');
      const accessToken = wx.getStorageSync('access_token');
      const localStatusCode = wx.getStorageSync('status_code');

      // 防止在登录过程中再次触发验证
      if (this.isVerifyingStatus) {
        console.log('[状态] 验证已在进行中，跳过');
        return;
      }

      this.isVerifyingStatus = true;

      try {
        // 发送状态验证请求
        const res = await new Promise((resolve, reject) => {
          wx.request({
            url: app.globalData.wangz + '/user/verifyStatus',
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            data: {
              user_id: userId,
              access_token: accessToken
            },
            success: resolve,
            fail: reject
          });
        });

        // 处理验证结果
        if (res.data.error_code === 0 && res.data.data) {
          const serverStatus = res.data.data.status;
          const serverStatusCode = res.data.data.status_code;

          console.log('[状态] 验证信息:', {
            localStatus,
            serverStatus,
            localStatusCode,
            serverStatusCode,
            match: localStatusCode === serverStatusCode
          });

          // 如果本地状态码与服务器状态码不一致，清除本地缓存并重新登录
          if (localStatusCode !== serverStatusCode) {
            console.log('[状态] 状态码不一致，清除缓存并重新登录');
            await this.logout();
            // 不要循环调用login，而是返回false让调用方处理
            return false;
          } else {
            // 更新本地存储和全局变量
            if (localStatus !== serverStatus) {
              console.log('[状态] 状态不一致，更新本地状态:', serverStatus);
              wx.setStorageSync('status', serverStatus);
              app.globalData.status = serverStatus;
            }
            return true;
          }
        } else {
          console.log('[状态] 验证失败:', res.data.msg);
          return true; // 验证失败也返回true，避免重复登录
        }
      } finally {
        this.isVerifyingStatus = false;
      }
    } catch (error) {
      console.error('[状态] 验证出错:', error);
      this.isVerifyingStatus = false;
      return true; // 出错也返回true，避免重复登录
    }
  }

  // 私有方法：微信登录
  _wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  }

  // 私有方法：服务器登录
  async _serverLogin(code) {
    try {
      const app = await this._getApp();
      console.log(code);
      return new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.wangz + '/user/wxLogin',
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: { code },
          success: resolve,
          fail: reject
        });
      });
    } catch (error) {
      console.error('[登录] 服务器登录失败:', error);
      throw error;
    }
  }

  // 私有方法：处理登录成功
  async _handleLoginSuccess(data) {
    try {
      // 先更新token
      await this._updateToken(data);

      // 如果没有用户信息，尝试获取用户信息
      if (!data.id) {
        const userInfoResponse = await this._getUserInfo(data.access_token);
        if (userInfoResponse.data.error_code === 0) {
          // 合并token和用户信息
          data = { ...data, ...userInfoResponse.data.data };
        } else {
          throw new Error('获取用户信息失败');
        }
      }

      // 保存完整的用户数据
      await this._saveUserData(data);

      // 登录成功后连接WebSocket
      const app = await this._getApp();
      if (app.globalData.websocket && app.globalData.user_id) {
        console.log('[登录] 使用userId初始化WebSocket');
        app.globalData.websocket.init(app.globalData.user_id);
      } else {
        console.error('[登录] 初始化WebSocket失败：缺少userId');
      }

      // 检查学校信息，如果没有则跳转到学校选择页面
      await this._checkSchoolInfo();

      return true;
    } catch (error) {
      console.error('[登录] 处理登录成功失败:', error);
      throw error;
    }
  }

  // 获取用户信息
  async _getUserInfo(access_token) {
    try {
      const app = await this._getApp();
      return new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.wangz + '/user/getUserInfo',
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${access_token}`
          },
          success: resolve,
          fail: reject
        });
      });
    } catch (error) {
      console.error('[登录] 获取用户信息失败:', error);
      throw error;
    }
  }

  // 私有方法：更新token
  async _updateToken(tokenData) {
    try {
      const app = await this._getApp();
      const { access_token, refresh_token, expire_in } = tokenData;

      if (!access_token) {
        throw new Error('服务器返回的token为空');
      }

      // 更新全局数据
      app.globalData.access_token = access_token;
      app.globalData.refresh_token = refresh_token;

      // 存储到本地
      wx.setStorageSync('access_token', access_token);
      wx.setStorageSync('refresh_token', refresh_token);

      // 设置过期时间（当前时间 + expire_in秒）
      const expireTime = Date.now() + (expire_in * 1000);
      wx.setStorageSync('token_expire', expireTime);

      console.log('[Token] 更新成功:', {
        access_token: access_token.substring(0, 10) + '...', // 只显示部分token
        refresh_token: refresh_token.substring(0, 10) + '...',
        expire_in,
        expireTime
      });

      app.globalData.isLoggedIn = true;
      return access_token;
    } catch (error) {
      console.error('[Token] 更新失败:', error);
      throw error;
    }
  }

  // 私有方法：保存用户数据
  async _saveUserData(data) {
    try {
      const app = await this._getApp();

      // 添加调试日志
      console.log('[登录] 保存用户数据，完整data:', data);

      // 保存用户数据到全局变量
      app.globalData.user_id = data.id;
      app.globalData.username = data.username;
      app.globalData.face_url = data.face_url;
      app.globalData.phone = data.phone;
      app.globalData.status = data.status;
      app.globalData.openid = data.openid;
      app.globalData.titlename = data.titlename;
      app.globalData.titlecolor = data.titlecolor;
      app.globalData.unread = data.unread || 0;
      app.globalData.status_code = data.status_code;
      app.globalData.school_id = data.school_id; // 保存school_id到全局变量
      app.globalData.uid = data.uid; // 保存uid到全局变量
      app.globalData.uid_formatted = data.uid_formatted; // 保存格式化uid到全局变量

      // 保存用户数据到本地存储
      wx.setStorageSync('user_id', data.id);
      wx.setStorageSync('username', data.username);
      wx.setStorageSync('face_url', data.face_url);
      wx.setStorageSync('phone', data.phone);
      wx.setStorageSync('status', data.status);
      wx.setStorageSync('openid', data.openid);
      wx.setStorageSync('titlename', data.titlename);
      wx.setStorageSync('titlecolor', data.titlecolor);
      wx.setStorageSync('unread', data.unread || 0);
      wx.setStorageSync('school_id', data.school_id); // 保存school_id到本地存储
      wx.setStorageSync('uid', data.uid); // 保存uid到本地存储
      wx.setStorageSync('uid_formatted', data.uid_formatted); // 保存格式化uid到本地存储

      // 保存认证学校信息
      if (data.verified_university_id) {
        wx.setStorageSync('verified_university_id', data.verified_university_id);
        app.globalData.verified_university_id = data.verified_university_id;
      }

      if (data.verified_school_info) {
        wx.setStorageSync('verified_school_info', data.verified_school_info);
        app.globalData.verified_school_info = data.verified_school_info;
      }


      console.log('[登录] 验证本地存储school_id:', wx.getStorageSync('school_id'));

      // 处理学校信息
      if (data.school_info) {
        // 如果后端返回了完整的学校信息，直接保存
        wx.setStorageSync('selected_school', data.school_info);
        wx.setStorageSync('has_school_info', true);
        console.log('[登录] 后端返回学校信息，设置用户学校:', data.school_info);
      } else if (data.school_id) {
        // 如果有school_id但没有完整信息，标记为有学校但需要获取详细信息
        wx.setStorageSync('has_school_info', true);
        console.log('[登录] 用户有school_id但缺少详细信息:', data.school_id);
      } else {
        // 如果没有学校信息，标记为未选择学校
        wx.setStorageSync('has_school_info', false);
        wx.removeStorageSync('selected_school');
        console.log('[登录] 用户未选择学校，需要引导选择');
      }

      // 保存状态码到本地存储
      if (data.status_code) {
        wx.setStorageSync('status_code', data.status_code);
      } else if (app.globalData.user_id) {
        // 如果没有状态码但有用户ID，则通过API获取状态码
        try {
          const res = await this._getUserStatus(app.globalData.user_id);
          if (res && res.data.error_code === 0 && res.data.data.status_code) {
            wx.setStorageSync('status_code', res.data.data.status_code);
            app.globalData.status_code = res.data.data.status_code;
          }
        } catch (error) {
          console.error('[登录] 获取状态码失败:', error);
        }
      }

      // 更新用户登录状态
      app.globalData.isLoggedIn = true;

      console.log('[登录] 用户数据保存成功:', {
        id: data.id,
        username: data.username,
        status: data.status,
        status_code: data.status_code || wx.getStorageSync('status_code')
      });

      return true;
    } catch (error) {
      console.error('[登录] 保存用户数据失败:', error);
      return false;
    }
  }

  // 获取用户状态信息
  async _getUserStatus(userId) {
    try {
      const app = await this._getApp();
      const accessToken = wx.getStorageSync('access_token');

      return new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.wangz + '/user/verifyStatus',
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            user_id: userId,
            access_token: accessToken
          },
          success: resolve,
          fail: reject
        });
      });
    } catch (error) {
      console.error('[登录] 获取用户状态失败:', error);
      throw error;
    }
  }





  // 退出登录
  async logout() {
    try {
      const app = await this._getApp();

      // 先关闭WebSocket连接
      if (app.globalData.websocket) {
        await new Promise(resolve => {
          app.globalData.websocket.close();
          setTimeout(resolve, 100); // 给一点时间让WebSocket完全关闭
        });
      }

      // 重置全局数据
      const defaultGlobalData = {
        username: '点击此处登录',
        face_url: 'https://www.bjgaoxiaoshequ.store/images/weixiao.png',
        user_id: '',
        phone: '',
        isLoggedIn: false,
        status: '',
        unread: 0,
        titlename: '无',
        titlecolor: '',
        openid: '',
        access_token: '',
        refresh_token: '',
        token_expire: '',
        userId: '',
        userInfo: null,
        uploadedImageUrls: ''
      };

      // 更新全局数据
      Object.keys(defaultGlobalData).forEach(key => {
        app.globalData[key] = defaultGlobalData[key];
      });

      // 获取当前存储的所有key
      const res = wx.getStorageInfoSync();
      const allKeys = res.keys;

      // 需要保留的key列表
      const keysToKeep = ['currentPage'];

      // 清除除了保留key之外的所有存储
      allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
          try {
            wx.removeStorageSync(key);
          } catch (e) {
            console.error('清除存储失败:', key, e);
          }
        }
      });

      // 额外确保这些关键信息被清除
      const criticalKeys = [
        'access_token',
        'refresh_token',
        'token_expire',
        'token',
        'uploadedImageUrls',
        'titlecolor'
      ];

      criticalKeys.forEach(key => {
        try {
          wx.removeStorageSync(key);
        } catch (e) {
          console.error('清除关键信息失败:', key, e);
        }
      });

      return true;
    } catch (error) {
      console.error('[登录] 退出登录失败:', error);
      return false;
    }
  }

  // 检查学校信息
  async _checkSchoolInfo() {
    try {
      const hasSchoolInfo = wx.getStorageSync('has_school_info');
      const selectedSchool = wx.getStorageSync('selected_school');
      const app = await this._getApp();
      const userId = app.globalData.user_id;

      console.log('[登录] 检查学校信息 - hasSchoolInfo:', hasSchoolInfo);
      console.log('[登录] 检查学校信息 - selectedSchool:', selectedSchool);
      console.log('[登录] 检查学校信息 - userId:', userId);

      // 只有在明确没有学校信息时才跳转
      if (hasSchoolInfo === false || (!hasSchoolInfo && !selectedSchool && userId)) {
        console.log('[登录] 用户确实没有学校信息，跳转到学校选择页面');

        // 立即跳转，使用reLaunch确保清空页面栈
        wx.reLaunch({
          url: '/pages/school-select/school-select'
        });
        return true; // 返回true表示已跳转
      }
      return false; // 返回false表示无需跳转
    } catch (error) {
      console.error('[登录] 检查学校信息失败:', error);
      return false;
    }
  }

  // 获取用户状态
  getUserStatus() {
    return wx.getStorageSync('status') || 'unverified';
  }

  // 检查用户是否已登录
  isLoggedIn() {
    const token = wx.getStorageSync('access_token');
    return !!token;
  }

  // 检查用户是否已认证
  isVerified() {
    const status = this.getUserStatus();
    return status === 'verified';
  }


}

// 导出单例实例
const loginManager = new LoginManager();
module.exports = { loginManager };