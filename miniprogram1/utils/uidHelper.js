/**
 * UID工具类 - 前端版本
 * 提供UID相关的便捷方法
 */
class UidHelper {
  /**
   * 获取当前用户的UID（数字）
   * @returns {number|null} UID数字，未设置则返回null
   */
  static getUid() {
    const app = getApp();
    return app.globalData.uid || wx.getStorageSync('uid') || null;
  }

  /**
   * 获取当前用户的格式化UID（6位字符串）
   * @returns {string|null} 格式化的UID，未设置则返回null
   */
  static getFormattedUid() {
    const app = getApp();
    return app.globalData.uid_formatted || wx.getStorageSync('uid_formatted') || null;
  }

  /**
   * 检查用户是否有UID
   * @returns {boolean} 是否有UID
   */
  static hasUid() {
    return this.getUid() !== null;
  }

  /**
   * 格式化UID为6位字符串（前端备用方法）
   * @param {number} uid 原始UID
   * @returns {string|null} 格式化的UID
   */
  static formatUid(uid) {
    if (uid === null || uid === undefined || uid === '') {
      return null;
    }
    return String(uid).padStart(6, '0');
  }

  /**
   * 获取UID显示文本
   * @returns {string} 用于显示的UID文本
   */
  static getDisplayText() {
    const formattedUid = this.getFormattedUid();
    if (formattedUid) {
      return `UID: ${formattedUid}`;
    }
    return '未分配UID';
  }

  /**
   * 刷新UID数据（从本地存储重新加载）
   */
  static refresh() {
    const app = getApp();
    app.globalData.uid = wx.getStorageSync('uid') || null;
    app.globalData.uid_formatted = wx.getStorageSync('uid_formatted') || null;
  }

  /**
   * 清除UID数据
   */
  static clear() {
    const app = getApp();
    app.globalData.uid = null;
    app.globalData.uid_formatted = null;
    wx.removeStorageSync('uid');
    wx.removeStorageSync('uid_formatted');
  }

  /**
   * 调试信息
   * @returns {object} UID相关的调试信息
   */
  static getDebugInfo() {
    const app = getApp();
    return {
      globalData_uid: app.globalData.uid,
      globalData_uid_formatted: app.globalData.uid_formatted,
      storage_uid: wx.getStorageSync('uid'),
      storage_uid_formatted: wx.getStorageSync('uid_formatted'),
      hasUid: this.hasUid(),
      displayText: this.getDisplayText()
    };
  }
}

module.exports = UidHelper;
