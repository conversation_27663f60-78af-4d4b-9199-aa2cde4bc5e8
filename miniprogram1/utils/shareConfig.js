/**
 * 统一分享配置工具
 * 简化版：只需要配置页面标题
 */

/**
 * 获取分享图片URL
 * @returns {string} 分享图片的完整URL
 */
function getShareImageUrl() {
  return getApp().globalData.wangz + '/uploads/jingyu.png';
}

/**
 * 获取分享给好友的配置
 * @param {Object} options 配置选项
 * @param {string} options.pageName 页面名称，如果不传则使用默认标题
 * @param {string} options.customTitle 自定义标题，优先级最高
 * @returns {Object} 分享配置对象
 */
function getShareAppMessageConfig(options = {}) {
  const { pageName, customTitle } = options;

  let title;
  if (customTitle) {
    // 使用自定义标题
    title = customTitle;
  } else if (pageName) {
    // 使用页面名称格式：鲸语校园 - 页面名字
    title = `鲸语校园 - ${pageName}`;
  } else {
    // 默认标题
    title = '北京最好的大学城树洞社区';
  }

  return {
    title: title,
    path: getCurrentPagePath(),
    imageUrl: getShareImageUrl(),
    success: function(res) {
      console.log('分享成功');
      wx.showToast({
        title: '分享成功',
        icon: 'success',
        duration: 1500
      });
    },
    fail: function(res) {
      console.log('分享失败');
    }
  };
}

/**
 * 获取分享到朋友圈的配置
 * @param {Object} options 配置选项
 * @param {string} options.pageName 页面名称，如果不传则使用默认标题
 * @param {string} options.query 分享参数
 * @param {string} options.customTitle 自定义标题，优先级最高
 * @returns {Object} 朋友圈分享配置对象
 */
function getShareTimelineConfig(options = {}) {
  const { pageName, query = '', customTitle } = options;

  let title;
  if (customTitle) {
    // 使用自定义标题
    title = customTitle;
  } else if (pageName) {
    // 使用页面名称格式：鲸语校园 - 页面名字
    title = `鲸语校园 - ${pageName}`;
  } else {
    // 默认标题
    title = '北京最好的大学城树洞社区';
  }

  return {
    title: title,
    query: query,
    imageUrl: getShareImageUrl()
  };
}

/**
 * 获取当前页面路径
 * @returns {string} 当前页面的完整路径
 */
function getCurrentPagePath() {
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    return '/' + currentPage.route;
  }
  return '/pages/fold1/home/<USER>';
}

/**
 * 为页面快速设置分享功能
 * @param {Object} pageInstance 页面实例 (this)
 * @param {Object} options 配置选项
 * @param {string} options.pageName 页面名称
 * @param {string} options.customTitle 自定义标题
 */
function setupPageShare(pageInstance, options = {}) {
  const { pageName, customTitle } = options;

  // 设置分享给好友
  pageInstance.onShareAppMessage = function() {
    return getShareAppMessageConfig({
      pageName: pageName,
      customTitle: customTitle
    });
  };

  // 设置分享到朋友圈
  pageInstance.onShareTimeline = function() {
    return getShareTimelineConfig({
      pageName: pageName,
      customTitle: customTitle
    });
  };
}

module.exports = {
  // 分享配置方法
  getShareImageUrl,
  getShareAppMessageConfig,
  getShareTimelineConfig,
  getCurrentPagePath,
  setupPageShare
};
