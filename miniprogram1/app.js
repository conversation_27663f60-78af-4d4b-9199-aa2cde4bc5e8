import websocket from './utils/websocket';
import { loginManager } from './utils/loginManager';
import eventBus from './utils/eventBus';

// 环境配置 - 注释掉不用的环境
const baseUrl = 'http://localhost:80/index.php';
// const baseUrl = 'https://www.bjgaoxiaoshequ.store';

// 根据域名自动配置其他参数
const isLocalEnvironment = baseUrl.includes('localhost');
const defaultFaceUrl = isLocalEnvironment ?
  'http://localhost:80/images/weixiao.png' :
  'https://www.bjgaoxiaoshequ.store/images/weixiao.png';
const secretKey = isLocalEnvironment ? 'DEV-BUAA-SSO-KEY-2024' : 'PROD-BUAA-SSO-KEY-2024';

App({
  globalData: {
    wangz: baseUrl,
    username: '点击此处登录',
    face_url: defaultFaceUrl,
    // 环境信息
    isLocalEnvironment: isLocalEnvironment,
    currentEnvironment: isLocalEnvironment ? 'development' : 'production',
    secretKey: secretKey,
    default_school_icon: '', // 默认学校图标，在onLaunch中初始化
    user_id: '',
    phone: '',
    isLoggedIn: false,
    status: '',
    unread: '',
    unreadComments: 0,  // 添加未读评论数
    unreadNotifications: 0,   // 添加未读系统通知数
    unreadActivityCount: 0, // 添加未读活动数
    activitySubscribed: true, // 默认订阅活动
    showOfficialAccountTip: true, // 默认显示公众号提示
    showHotTopic: true, // 默认显示每日热榜
    titlename: '无',
    openid: '',
    access_token: '',
    refresh_token: '',
    school_id: '', // 添加用户学校ID
    uid: null, // 用户UID（数字）
    uid_formatted: null, // 格式化的UID（6位字符串）
    websocket: websocket,
    isLoginReady: false,
    loginManager: loginManager,
    loginListeners: [],
    eventBus: eventBus,

    // 获取用户UID的便捷方法
    getUserUid() {
      return this.uid || wx.getStorageSync('uid') || null;
    },

    // 获取格式化UID的便捷方法
    getUserUidFormatted() {
      return this.uid_formatted || wx.getStorageSync('uid_formatted') || null;
    },

    updateLikeNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      return new Promise((resolve) => {
        wx.request({
          url: `${this.wangz}/notification/getMessages`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            type: 'likes',
            page: 1,
            page_size: 1
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 使用unread_count字段获取未读总数
              this.unread = res.data.unread_count || 0;
              resolve();
            } else {
              resolve();
            }
          },
          fail: (error) => {
            resolve();
          }
        });
      });
    },
    updateCommentNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      return new Promise((resolve) => {
        wx.request({
          url: `${this.wangz}/notification/getMessages`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            type: 'replies',
            page: 1,
            page_size: 1
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 使用unread_count字段获取未读总数
              this.unreadComments = res.data.unread_count || 0;
              resolve();
            } else {
              resolve();
            }
          },
          fail: (error) => {
            resolve();
          }
        });
      });
    },
    updateReplyNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      return new Promise((resolve) => {
        wx.request({
          url: `${this.wangz}/notification/getMessages`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'token': wx.getStorageSync('access_token')
          },
          data: {
            type: 'notifications',
            page: 1,
            page_size: 1
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 使用unread_count字段获取未读总数
              this.unreadNotifications = res.data.unread_count || 0;
              resolve();
            } else {
              resolve();
            }
          },
          fail: () => {
            resolve();
          }
        });
      });
    },
    async updateAllNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      try {
        // 并行获取所有类型的通知
        await Promise.all([
          this.updateLikeNotifications(),
          this.updateCommentNotifications(),
          this.updateReplyNotifications()
        ]);

        // 计算新的通知总数
        const newTotal = parseInt(this.unread || 0) +
                        parseInt(this.unreadComments || 0) +
                        parseInt(this.unreadNotifications || 0);

        // 获取当前页面实例
        const pages = getCurrentPages();
        const mePage = pages.find(page => page.route === 'pages/fold3/me/me');

        // 如果个人中心页面存在，更新其通知数量
        if (mePage) {
          mePage.updateGridNotifications();
        }

        // 检查当前是否在TabBar页面
        const currentPage = pages[pages.length - 1];
        const isTabBarPage = currentPage &&
          ['pages/fold1/home/<USER>', 'pages/fold2/xuqiu/xuqiu',
           'pages/fold3/me/me', 'pages/fold4/gongju/gongju'].includes(currentPage.route);

        // 只在TabBar页面上才设置徽章
        if (isTabBarPage && newTotal > 0) {
          wx.setTabBarBadge({
            index: 3,
            text: newTotal.toString()
          }).catch(() => {});
        }
      } catch (error) {
        // 保留错误处理但不打印日志
      }
    },
    addLoginListener(callback) {
      this.loginListeners.push(callback);
    },
    removeLoginListener(callback) {
      const index = this.loginListeners.indexOf(callback);
      if (index > -1) {
        this.loginListeners.splice(index, 1);
      }
    },
    // 初始化通知相关变量
    initNotifications() {
      this.unread = 0;
      this.unreadComments = 0;
      this.unreadNotifications = 0;
      this.lastTotalCount = 0;
      this.unreadActivityCount = 0;
    },
    // 清除活动相关的所有红点和角标
    clearActivityBadge() {
      // 清除未读计数并移除TabBar红点
      this.clearActivityNotifications();

      // 尝试更新工具页中活动专区按钮的badge
      try {
        // 获取当前页面栈
        const pages = getCurrentPages();
        // 找到工具页面
        const gongjuPage = pages.find(page => page.route === 'pages/fold4/gongju/gongju');

        // 如果找到工具页面，清除其角标
        if (gongjuPage) {
          const grid4 = gongjuPage.data.grid4;
          if (grid4 && grid4.length > 2) {
            grid4[2].badge = 0; // 活动专区在grid4中的索引为2
            gongjuPage.setData({ grid4 });
          }
        }
      } catch (e) {
        console.error('清除活动专区badge失败:', e);
      }
    },
    // 检查新活动通知
    checkNewActivities() {
      // 获取用户ID
      const userId = this.user_id || '';

      // 如果用户ID为空，不进行请求
      if (!userId) {
        console.log('用户ID为空，跳过新活动检查');
        return;
      }

      // 从本地存储获取订阅状态
      const isSubscribed = wx.getStorageSync('activitySubscribe');
      // 如果没有设置，默认为true
      const subscribed = isSubscribed !== "" ? isSubscribed : true;

      // 更新全局变量
      this.activitySubscribed = subscribed;

      // 如果用户未订阅，不显示红点
      if (!subscribed) {
        this.clearActivityNotifications();
        return;
      }

      console.log('检查新活动, userId:', userId);

      wx.request({
        url: this.wangz + '/activity/getNewActivityCount',
        method: 'POST',
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'Authorization': wx.getStorageSync('access_token')
        },
        data: {
          user_id: userId,
          subscribed: subscribed ? '1' : '0'
        },
        success: (res) => {
          console.log('新活动检查响应:', res.data);

          if (res.data.code === 200) {
            const count = res.data.data.count;

            // 更新全局状态
            this.unreadActivityCount = count;

            // 保存到本地存储
            wx.setStorageSync('unreadActivityCount', count);

            // 如果服务器返回了最后查看时间，更新本地缓存
            if (res.data.data.last_view_time) {
              wx.setStorageSync('activityLastViewTime', res.data.data.last_view_time);
            }

            // 根据未读计数更新红点
            this.updateActivityBadge(count);
          } else {
            // 请求失败也清除红点
            this.clearActivityNotifications();
          }
        },
        fail: (err) => {
          console.error('检查新活动失败:', err);
          // 请求失败清除红点
          this.clearActivityNotifications();
        }
      });
    },

    // 根据未读计数更新活动红点
    updateActivityBadge(count) {
      if (count > 0) {
        console.log(`显示红点：有${count}个新活动`);

        // 设置TabBar右上角的数字
        wx.setTabBarBadge({
          index: 2, // 工具页是第3个tab（索引为2）
          text: count.toString() // 转为字符串
        }).catch(() => {
          // 忽略可能的错误
        });
      } else {
        console.log('移除红点：没有新活动');

        // 移除TabBar上的数字红点
        wx.removeTabBarBadge({
          index: 2
        }).catch(() => {
          // 忽略可能的错误
        });
      }
    },

    // 清除活动通知
    clearActivityNotifications() {
      // 确保TabBar没有红点
      wx.removeTabBarBadge({
        index: 2
      }).catch(() => {
        // 忽略可能的错误
      });
      // 清除未读计数
      this.unreadActivityCount = 0;
      wx.setStorageSync('unreadActivityCount', 0);
    },
  },

  onLaunch() {
    // 初始化默认学校图标
    this.globalData.default_school_icon = this.globalData.wangz + '/uploads/jingyu.png';

    // 从本地存储加载成绩缓存数据
    this.loadLocalGradeCache();

    // 从本地存储读取状态
    const activitySubscribed = wx.getStorageSync('activitySubscribe');
    const showOfficialAccountTip = wx.getStorageSync('showOfficialAccountTip');
    const showHotTopic = wx.getStorageSync('showHotTopic');

    // 初始化UID数据
    this.globalData.uid = wx.getStorageSync('uid') || null;
    this.globalData.uid_formatted = wx.getStorageSync('uid_formatted') || null;

    if (activitySubscribed !== "") {
      this.globalData.activitySubscribed = activitySubscribed;
    }

    if (showOfficialAccountTip !== "") {
      this.globalData.showOfficialAccountTip = showOfficialAccountTip;
    }

    if (showHotTopic !== "") {
      this.globalData.showHotTopic = showHotTopic;
    }

    // 初始化通知相关变量
    this.globalData.initNotifications();

    // 初始化活动订阅状态
    if (wx.getStorageSync('activitySubscribe') === '') {
      // 如果从未设置过，默认为true
      wx.setStorageSync('activitySubscribe', true);
    }

    // 将订阅状态保存到全局变量
    this.globalData.activitySubscribed = wx.getStorageSync('activitySubscribe');

    // 初始化loginManager
    loginManager.init(this).then(() => {
      // 启动时立即执行自动登录
      return loginManager.autoLogin();
    }).then((loginSuccess) => {
      this.globalData.isLoginReady = true;
      // 通知所有监听器登录已就绪
      this.globalData.loginListeners.forEach(callback => callback(loginSuccess));

      // 如果登录成功，检查学校信息并延迟预加载成绩数据
      if (loginSuccess) {
        this.checkSchoolInfoOnLaunch();

        // 延迟3秒后开始静默预加载，确保用户操作不受影响
        setTimeout(() => {
          this.silentPreloadGradeData();
        }, 3000);
      }

      // 只有在登录成功且有user_id的情况下才初始化WebSocket
      // if (loginSuccess && this.globalData.user_id) {
      //   console.log('[WebSocket] 初始化，userId:', this.globalData.user_id);
      //   this.globalData.websocket.init(this.globalData.user_id);
      // } else {
      //   console.log('[WebSocket] 初始化失败：未登录或无userId');
      // }
    }).catch(error => {
      console.error('[App] 自动登录失败:', error);
      this.globalData.isLoginReady = true;
      this.globalData.loginListeners.forEach(callback => callback(false));
    });

    // 加载字体
    wx.loadFontFace({
      family: '阿里妈妈东方大楷 Regular',
      global: true,
      source: 'url("https://www.bjgaoxiaoshequ.store/阿里妈妈东方大楷/Ar.woff2")',
    });
    wx.loadFontFace({
      family: '阿里妈妈刀隶体 Regular',
      global: true,
      source: 'url("https://www.bjgaoxiaoshequ.store/阿里妈妈刀隶体/At.woff2")'
    });

    // 监听网络状态
    wx.onNetworkStatusChange((res) => {
      if (res.isConnected && !this.globalData.isLoggedIn) {
        console.log('[登录] 网络重新连接，尝试重新登录');
        loginManager.login(true).catch(error => {
          console.error('[App] 重连登录失败:', error);
        });
      }
    });

    // 添加请求拦截器
    const originalRequest = wx.request;
    Object.defineProperty(wx, 'request', {
      configurable: true,
      enumerable: true,
      writable: true,
      value: (options) => {
        // 自动添加 user_id 到请求数据中
        if (options.data && typeof options.data === 'object') {
          options.data.user_id = this.globalData.user_id;
        }
        return originalRequest.call(wx, options);
      }
    });

    // 重写navigateTo方法，处理页面跳转逻辑
    const originalNavigateTo = wx.navigateTo;
    Object.defineProperty(wx, 'navigateTo', {
      configurable: true,
      enumerable: true,
      writable: true,
      value: (options) => {
        const url = options.url;
        const app = this;

        // 如果跳转到"点赞我的"页面，先发送已读请求
        if (url.includes('likes') && !url.includes('myLikes') && this.globalData.user_id && this.globalData.isLoggedIn) {
          wx.request({
            url: `${this.globalData.wangz}/notification/markRead`,
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded',
              'token': wx.getStorageSync('access_token')
            },
            data: {
              type: 'likes'
            },
            success: (res) => {
              if (res.data.code === 200) {
                this.globalData.unread = 0;

                // 不再设置本地存储标记
                // 直接更新UI显示
                try {
                  // 尝试找到并更新我的页面
                  const pages = getCurrentPages();
                  const mePage = pages.find(page => page.route === 'pages/fold3/me/me');
                  if (mePage && mePage.updateGridNotifications) {
                    mePage.updateGridNotifications();
                  }
                } catch (e) {
                  // 错误处理
                }
              }
            },
            fail: (err) => {
              // 错误处理
            },
            complete: () => {
              // 无论请求成功还是失败，都继续页面跳转
              originalNavigateTo.call(wx, options);
            }
          });
        }
        // 如果跳转到"评论我的"页面，清除评论回复的未读通知
        else if (url.includes('reply') && this.globalData.user_id && this.globalData.isLoggedIn) {
          wx.request({
            url: `${this.globalData.wangz}/notification/markRead`,
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded',
              'token': wx.getStorageSync('access_token')
            },
            data: {
              type: 'replies'
            },
            success: (res) => {
              if (res.data.code === 200) {
                this.globalData.unreadComments = 0;
              }
            },
            fail: (err) => {
              // 错误处理
            },
            complete: () => {
              // 无论请求成功还是失败，都继续页面跳转
              originalNavigateTo.call(wx, options);
            }
          });
        }
        // 如果跳转到"消息通知"页面，清除系统通知的未读通知
        else if (url.includes('notifications') && this.globalData.user_id && this.globalData.isLoggedIn) {
          wx.request({
            url: `${this.globalData.wangz}/notification/markRead`,
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded',
              'token': wx.getStorageSync('access_token')
            },
            data: {
              type: 'notifications'
            },
            success: (res) => {
              if (res.data.code === 200) {
                this.globalData.unreadNotifications = 0;
              }
            },
            fail: (err) => {
              // 错误处理
            },
            complete: () => {
              // 无论请求成功还是失败，都继续页面跳转
              originalNavigateTo.call(wx, options);
            }
          });
        } else {
          // 其他页面正常跳转
          originalNavigateTo.call(wx, options);
        }
      }
    });
  },

  // 检查登录状态的方法
  checkLoginReady() {
    return new Promise((resolve) => {
      if (this.globalData.isLoginReady) {
        resolve(this.globalData.isLoggedIn);
      } else {
        const callback = (loginSuccess) => {
          this.globalData.removeLoginListener(callback);
          resolve(loginSuccess);
        };
        this.globalData.addLoginListener(callback);
      }
    });
  },

  // 发送WebSocket消息
  sendWebSocketMessage(data) {
    if (this.globalData.websocket) {
      return this.globalData.websocket.send(data);
    }
    return Promise.reject(new Error('WebSocket not initialized'));
  },

  // 关闭WebSocket连接
  closeWebSocket() {
    if (this.globalData.websocket) {
      this.globalData.websocket.close();
    }
  },

  onHide() {
    // 进入后台时关闭WebSocket连接
    this.closeWebSocket();
  },

  onShow() {
    // 只在用户已登录时检查新活动
    if (this.globalData.isLoggedIn && this.globalData.user_id) {
      // 检查新活动
      this.globalData.checkNewActivities();
    } else {
      console.log('用户未登录，跳过活动检查');
    }

    // 回到前台时，如果已登录则重新连接WebSocket并更新通知
    if (this.globalData.isLoggedIn && this.globalData.access_token) {
      this.globalData.websocket.reconnect();
      this.globalData.updateAllNotifications();  // 添加更新所有通知的调用
    }
  },

  // 检查学校信息（应用启动时）
  checkSchoolInfoOnLaunch() {
    try {
      const hasSchoolInfo = wx.getStorageSync('has_school_info');
      const selectedSchool = wx.getStorageSync('selected_school');
      const userId = this.globalData.user_id;

      console.log('[App] 启动时检查学校信息 - hasSchoolInfo:', hasSchoolInfo);
      console.log('[App] 启动时检查学校信息 - selectedSchool:', selectedSchool);
      console.log('[App] 启动时检查学校信息 - userId:', userId);

      // 只有在明确没有学校信息且用户已登录时才跳转
      if ((hasSchoolInfo === false || (!hasSchoolInfo && !selectedSchool)) && userId) {
        console.log('[App] 用户确实没有学校信息，跳转到学校选择页面');

        // 延迟一点时间确保页面加载完成
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/school-select/school-select'
          });
        }, 1000);
      }
    } catch (error) {
      console.error('[App] 检查学校信息失败:', error);
    }
  },

  // 静默预加载成绩数据（默认学期及前后两个学期）
  silentPreloadGradeData() {
    try {
      const ssoUsername = wx.getStorageSync('sso_username');
      const ssoPassword = wx.getStorageSync('sso_password');

      if (!ssoUsername || !ssoPassword) {
        return;
      }

      // 计算默认学期
      const defaultSemester = this.calculateDefaultSemester();
      if (!defaultSemester) {
        return;
      }

      // 计算需要预加载的学期列表（默认学期及前后两个学期）
      const semestersToPreload = this.calculateSemestersToPreload(defaultSemester);



      // 依次预加载每个学期的数据，错开请求时间避免影响用户体验
      semestersToPreload.forEach((semesterInfo, index) => {
        setTimeout(() => {
          this.silentPreloadSemesterData(semesterInfo, ssoUsername, ssoPassword);
        }, index * 500); // 增加间隔时间，更加静默
      });

    } catch (error) {
      // 静默处理错误，不影响用户体验
    }
  },

  // 计算默认学期
  calculateDefaultSemester() {
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();

    let academicYear, semester;
    if (currentMonth >= 3 && currentMonth <= 9) {
      // 3-9月显示当年的第二学期（春季学期）
      academicYear = `${currentYear - 1}-${currentYear}`;
      semester = 2;
    } else {
      // 10-12月和1-2月显示对应学年的第一学期（秋季学期）
      if (currentMonth >= 10) {
        // 10-12月，显示当年开始的学年第一学期
        academicYear = `${currentYear}-${currentYear + 1}`;
        semester = 1;
      } else {
        // 1-2月，显示上一年开始的学年第一学期
        academicYear = `${currentYear - 1}-${currentYear}`;
        semester = 1;
      }
    }

    // 验证学期数据的有效性
    if (!academicYear || !academicYear.includes('-') || !semester || ![1, 2, 3].includes(semester)) {
      console.error('[预加载] 学期数据无效:', { academicYear, semester });
      return null;
    }

    return { academicYear, semester };
  },

  // 计算需要预加载的学期列表
  calculateSemestersToPreload(defaultSemester) {
    const semesters = [];
    const { academicYear, semester } = defaultSemester;

    // 添加前两个学期
    for (let i = 1; i <= 2; i++) {
      const prev = this.getPreviousSemester(academicYear, semester, i);
      semesters.push(prev);
    }

    // 添加默认学期
    semesters.push({ academicYear, semester });

    // 添加后两个学期
    for (let i = 1; i <= 2; i++) {
      const next = this.getNextSemester(academicYear, semester, i);
      semesters.push(next);
    }

    return semesters;
  },

  // 静默预加载单个学期的数据
  silentPreloadSemesterData(semesterInfo, ssoUsername, ssoPassword) {
    const { academicYear, semester } = semesterInfo;
    const cacheKey = `${academicYear}-${semester}`;

    // 检查是否已经有本地缓存
    try {
      const localGradeCache = wx.getStorageSync('gradeCache') || {};
      if (localGradeCache[cacheKey]) {
        return; // 已有缓存，跳过
      }
    } catch (error) {
      // 静默处理错误
    }

    wx.request({
      url: this.globalData.wangz + '/BuaaProxy/getScoreOneStep',
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('access_token')
      },
      data: {
        username: ssoUsername,
        password: ssoPassword,
        year: academicYear,
        xq: semester
      },
      success: (res) => {
        if (res.data && res.data.error_code === 0) {
          const gradeData = {
            gradeList: res.data.data?.d || [],
            totalCredits: this.calculateTotalCredits(res.data.data?.d || []),
            userGPA: res.data.data?.gpa || '0.00',
            cookies: res.data.cookies
          };

          // 静默保存到本地存储
          try {
            const localGradeCache = wx.getStorageSync('gradeCache') || {};
            localGradeCache[cacheKey] = gradeData;
            wx.setStorageSync('gradeCache', localGradeCache);
          } catch (error) {
            // 静默处理错误
          }
        }
      },
      fail: (error) => {
        // 静默处理错误
      }
    });
  },

  // 获取向后第n个学期
  getNextSemester(baseYear, baseSemester, steps) {
    let year = baseYear;
    let semester = baseSemester + steps;

    while (semester > 3) {
      semester -= 3;
      const yearParts = year.split('-');
      const startYear = parseInt(yearParts[0]) + 1;
      year = `${startYear}-${startYear + 1}`;
    }

    return { academicYear: year, semester };
  },

  // 获取向前第n个学期
  getPreviousSemester(baseYear, baseSemester, steps) {
    let year = baseYear;
    let semester = baseSemester - steps;

    while (semester < 1) {
      semester += 3;
      const yearParts = year.split('-');
      const startYear = parseInt(yearParts[0]) - 1;
      year = `${startYear}-${startYear + 1}`;
    }

    return { academicYear: year, semester };
  },

  // 计算总学分
  calculateTotalCredits(scores) {
    let totalCredits = 0;
    scores.forEach(score => {
      const credit = parseFloat(score.xf) || 0;
      totalCredits += credit;
    });
    return totalCredits.toFixed(1);
  },

  // 从本地存储加载成绩缓存数据
  loadLocalGradeCache() {
    try {
      const localGradeCache = wx.getStorageSync('gradeCache');
      if (localGradeCache && typeof localGradeCache === 'object') {
        // 清理过期缓存（超过30天的数据）
        const cleanedCache = this.cleanExpiredCache(localGradeCache);
        this.globalData.gradeCache = cleanedCache;

        // 如果清理后的缓存与原缓存不同，更新本地存储
        if (Object.keys(cleanedCache).length !== Object.keys(localGradeCache).length) {
          wx.setStorageSync('gradeCache', cleanedCache);
          console.log('[本地缓存] 清理过期缓存，剩余:', Object.keys(cleanedCache));
        } else {
          console.log('[本地缓存] 成功加载本地成绩缓存:', Object.keys(localGradeCache));
        }
      } else {
        this.globalData.gradeCache = {};
        console.log('[本地缓存] 没有本地成绩缓存数据');
      }
    } catch (error) {
      console.error('[本地缓存] 加载本地成绩缓存失败:', error);
      this.globalData.gradeCache = {};
    }
  },

  // 清理过期缓存
  cleanExpiredCache(cache) {
    const now = Date.now();
    const thirtyDays = 30 * 24 * 60 * 60 * 1000; // 30天的毫秒数
    const cleanedCache = {};

    Object.keys(cache).forEach(key => {
      const data = cache[key];

      // 检查key格式是否正确（应该是 "YYYY-YYYY-N" 格式）
      const keyPattern = /^\d{4}-\d{4}-[123]$/;
      if (!keyPattern.test(key)) {
        console.log('[本地缓存] 清理异常缓存key:', key);
        return; // 跳过异常格式的key
      }

      // 检查数据完整性和时效性
      if (data &&
          data.timestamp &&
          (now - data.timestamp) < thirtyDays &&
          Array.isArray(data.gradeList) &&
          typeof data.totalCredits === 'string' &&
          typeof data.userGPA === 'string') {
        cleanedCache[key] = data;
      } else {
        console.log('[本地缓存] 清理无效或过期缓存:', key);
      }
    });

    return cleanedCache;
  },

  // 清理所有成绩缓存（用于调试或重置）
  clearAllGradeCache() {
    try {
      wx.removeStorageSync('gradeCache');
      this.globalData.gradeCache = {};
      console.log('[本地缓存] 已清理所有成绩缓存');
      return true;
    } catch (error) {
      console.error('[本地缓存] 清理缓存失败:', error);
      return false;
    }
  }
});