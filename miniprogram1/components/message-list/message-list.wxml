<!--components/message-list/message-list.wxml-->
<!-- published类型UI已删除，使用专门的mypub页面 -->

<!-- 消息列表 - 使用通知样式 -->
<view class="message-list-container">
  <!-- 加载状态 -->
  <block wx:if="{{loading && messages.length === 0}}">
    <view class="loading-container">
      <view class="loading-dots"></view>
    </view>
  </block>

  <!-- 错误状态 -->
  <block wx:elif="{{error}}">
    <view class="error-container">
      <image src="/images/cuowu.png" class="error-icon"/>
      <text class="error-text">{{error}}</text>
      <button class="retry-btn" bindtap="onRetry">重试</button>
    </view>
  </block>

  <!-- 消息列表 -->
  <block wx:elif="{{messages.length > 0}}">
    <scroll-view
      class="message-scroll"
      scroll-y
      refresher-enabled
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onRefresh"
      bindscrolltolower="onLoadMore">

      <view class="message-list">
        <view class="message-item {{item.highlighted ? 'highlighted' : ''}}"
              wx:for="{{messages}}"
              wx:key="id"
              bindtap="onMessageClick"
              data-message="{{item}}">

          <!-- 头像 -->
          <view class="user-avatar">
            <image class="avatar" src="{{item.avatar}}" mode="aspectFill"/>
          </view>

          <!-- 消息内容 -->
          <view class="message-content">
            <!-- 左侧主要内容 -->
            <view class="main-content">
              <!-- 用户信息 -->
              <view class="user-info">
                <!-- 用户名单独一行 -->
                <view class="user-name-line">
                  <text class="user-name">{{item.username}}</text>
                </view>
                <!-- 动作描述和时间在同一行 -->
                <view class="action-time-line">
                  <text class="action-desc">{{item.actionText}}</text>
                  <text class="time">{{item.time}}</text>
                </view>
              </view>

              <!-- 原始内容引用 -->
              <view wx:if="{{item.originalContent}}" class="original-content">
                <text class="original-text" user-select="true">{{item.originalContent}}</text>
              </view>

              <!-- 图片列表 -->
              <view wx:if="{{item.images && item.images.length > 0}}" class="content-images">
                <image
                  wx:for="{{item.images}}"
                  wx:key="*this"
                  wx:for-item="image"
                  src="{{image}}"
                  mode="aspectFill"
                  class="content-image"
                />
              </view>
            </view>

            <!-- 右侧引用内容图片 -->
            <view wx:if="{{item.originalImage}}" class="reference-image">
              <image src="{{item.originalImage}}" mode="aspectFill" class="ref-img"></image>
            </view>
          </view>


        </view>
      </view>

      <!-- 展开历史按钮 -->
      <view wx:if="{{showExpandHistory}}" class="expand-history">
        <button
          class="expand-btn {{loadingHistory ? 'loading' : ''}}"
          bindtap="expandHistory"
          disabled="{{loadingHistory}}">
          <text wx:if="{{!loadingHistory}}">展开历史{{type === 'likes' ? '点赞' : type === 'replies' ? '评论' : '通知'}}</text>
          <text wx:else>加载中...</text>
        </button>
      </view>

      <!-- 加载更多 -->
      <view wx:elif="{{loadingMore}}" class="loading-more">
        <view class="loading-dots small"></view>
        <text>加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view wx:elif="{{!hasMore && messages.length > 0}}" class="no-more">
        <text>没有更多了</text>
      </view>
    </scroll-view>
  </block>

  <!-- 空状态 -->
  <view wx:else class="empty-tip">
    <image src="{{emptyConfig.icon}}" mode="aspectFit" class="empty-image"/>
    <text>{{emptyConfig.text}}</text>
  </view>
</view>
