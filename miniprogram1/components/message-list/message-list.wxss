/* components/message-list/message-list.wxss */

/* 我发布的内容样式 - 使用原来的卡片布局 */
.published-container {
  min-height: 100vh;
  height: auto;
  background-color: rgb(244, 244, 249);
}

.content {
  padding: 0;
}

.num-item {
  box-sizing: border-box;
  border-radius: 34rpx;
  margin: 35rpx;
  padding: 20rpx 25rpx 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  width: 90%;
  background-color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: auto;
  height: auto !important;
}

.text {
  font-size: 32rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: pre-wrap;
  padding: 0;
}

.touxiang1{
  margin-bottom: 15rpx;
  height: 70rpx;
  display: flex;
  background-color:transparent;
  border: 3rpx ;
  align-items: flex-start;
  justify-content: space-between;
}

.user-avatar-img {
  height: 70rpx;
  width: 70rpx;
  border-radius: 15%;
}

.image-container {
  margin-top: 8rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.image-item {
  margin-left: 0rpx;
  flex: 0 0 auto;
  margin-right: 18rpx;
}

.uniform-image {
  width: 198rpx;
  height: 198rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.kuang {
  height: 60rpx;
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.last{
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 35rpx;
  line-height: 35rpx;
}

.action-icon {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
}

.gradient-text {
  font-size: 24rpx;
  color: #808080;
  line-height: 35rpx;
}

/* 通用样式 */
.message-list-container {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

.message-scroll {
  height: 100%;
}

.message-list {
  width: 100%;
  padding-top: 16rpx;
  background-color: #ffffff;
}

.message-item {
  display: flex;
  background: #ffffff;
  padding: 24rpx 32rpx;
  position: relative;
  align-items: flex-start;
}

.message-item::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1px;
  background-color: #f0f0f0;
}

.message-item:last-child::after {
  display: none;
}

.message-item.highlighted {
  background-color: #f8f9fa;
}

.message-item:active {
  background-color: #f5f5f5;
}

.user-avatar {
  margin-right: 24rpx;
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
}

.unread-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid white;
}

.message-content {
  flex: 1;
  min-width: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.main-content {
  flex: 1;
  min-width: 0;
}

/* 用户信息区域 */
.user-info {
  margin-bottom: 12rpx;
}

/* 用户名行 */
.user-name-line {
  margin-bottom: 4rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 动作描述和时间行 */
.action-time-line {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.action-desc {
  font-size: 28rpx;
  color: #666;
}

.time {
  font-size: 26rpx;
  color: #999;
}

.content-text {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.content-text text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.content-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.content-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.original-content {
  padding: 16rpx 0;
  border-left: 6rpx solid #ff4757;
  padding-left: 16rpx;
  margin-top: 8rpx;
  position: relative;
}

.original-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 右侧引用图片 */
.reference-image {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.ref-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

/* 右侧内容区域 */
.right-content {
  margin-left: 16rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
}

.right-image {
  margin-bottom: 8rpx;
}

.content-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

/* 操作标签 */
.right-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.action-tags {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-tag {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  text-align: center;
  min-width: 100rpx;
  white-space: nowrap;
}

.action-tag.primary {
  background-color: #f5f5f5;
  color: #666;
}

.action-tag.secondary {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-dots {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-more text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}

.no-more text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 260rpx 0;
  margin-top: -100rpx;
}

.empty-image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.empty-tip text {
  color: #999;
  font-size: 28rpx;
}

/* 展开历史按钮 */
.expand-history {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.expand-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.expand-btn:not(.loading):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.expand-btn.loading {
  opacity: 0.7;
  background: #ccc;
}

.expand-btn::after {
  border: none;
}
