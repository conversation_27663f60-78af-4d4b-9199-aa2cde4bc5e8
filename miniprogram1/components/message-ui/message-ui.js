// components/message-ui/message-ui.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 消息列表数据
    messages: {
      type: Array,
      value: []
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 加载更多状态
    loadingMore: {
      type: Boolean,
      value: false
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      value: true
    },
    // 空状态配置
    emptyConfig: {
      type: Object,
      value: {
        icon: '/images/xiaoxi.png',
        text: '暂无消息'
      }
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 是否显示展开历史按钮
    showExpandHistory: {
      type: Boolean,
      value: false
    },
    // 展开历史加载状态
    loadingHistory: {
      type: Boolean,
      value: false
    },
    // 消息类型（用于按钮文字）
    messageType: {
      type: String,
      value: ''
    },
    // 展开按钮文字类型
    expandButtonText: {
      type: String,
      value: '历史'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },



  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 消息项点击事件
     */
    onItemClick(e) {
      const { item, index } = e.currentTarget.dataset
      
      // 触发点击事件，传递消息数据
      this.triggerEvent('itemclick', {
        item: item,
        index: index
      })
    },

    /**
     * 滚动到底部事件
     */
    onScrollToLower() {
      this.triggerEvent('loadmore')
    },

    /**
     * 下拉刷新事件
     */
    onRefresh() {
      this.triggerEvent('refresh')
    },

    /**
     * 展开历史消息
     */
    onExpandHistory() {
      this.triggerEvent('expandhistory')
    }
  }
})
