<!--components/message-ui/message-ui.wxml-->
<view class="message-ui-container">
  <view class="message-list">
    <block wx:if="{{loading}}">
      <view class="loading-container">
        <view class="loading-dots"></view>
      </view>
    </block>
    <block wx:elif="{{messages.length > 0}}">
      <view class="message-item {{item.highlighted ? 'highlighted' : ''}}"
            wx:for="{{messages}}"
            wx:key="id"
            bindtap="onItemClick"
            data-item="{{item}}"
            data-index="{{index}}">
        <view class="user-avatar">
          <image class="avatar" src="{{item.avatar || '/images/weixiao.png'}}" mode="aspectFill"/>
        </view>
        <view class="message-content">
          <view class="user-name">{{item.username}}</view>
          <view class="action-line">
            <view class="action-text">
              {{item.actionText}}
              <text class="time">{{item.time}}</text>
            </view>
          </view>
          <!-- 引用内容 -->
          <view wx:if="{{item.originalContent}}" class="comment-content">
            <view class="quote-mark"></view>
            <text class="comment-text">{{item.originalContent}}</text>
          </view>
          <!-- 消息内容 -->
          <view wx:if="{{item.content && item.content !== item.originalContent}}" class="message-text">
            <text>{{item.content}}</text>
          </view>
          <!-- 图片列表 -->
          <view wx:if="{{item.images && item.images.length > 0}}" class="content-images">
            <image
              wx:for="{{item.images}}"
              wx:key="*this"
              wx:for-item="image"
              src="{{image}}"
              mode="aspectFill"
              class="content-image"
            />
          </view>
        </view>
        <!-- 右侧引用图片 -->
        <view class="right-image" wx:if="{{item.originalImage}}">
          <image class="content-thumb" src="{{item.originalImage}}" mode="aspectFill"/>
        </view>
      </view>
    </block>
    <view wx:else class="empty-tip">
      <image src="{{emptyConfig.icon}}" mode="aspectFit" class="empty-image"/>
      <text>{{emptyConfig.text}}</text>
    </view>
  </view>

  <!-- 展开历史按钮 -->
  <view wx:if="{{showExpandHistory}}" class="expand-history">
    <button
      class="expand-btn {{loadingHistory ? 'loading' : ''}}"
      bindtap="onExpandHistory"
      disabled="{{loadingHistory}}">
      <text wx:if="{{!loadingHistory}}">
        <block wx:if="{{expandButtonText === '剩余未读'}}">展开剩余未读{{messageType}}</block>
        <block wx:else>展开历史{{messageType}}</block>
      </text>
      <text wx:else>加载中...</text>
    </button>
  </view>

  <!-- 加载更多 -->
  <view wx:elif="{{loadingMore}}" class="loading-more">
    <view class="loading-dots small"></view>
    <text>加载中...</text>
  </view>

  <!-- 没有更多 -->
  <view wx:elif="{{!hasMore && messages.length > 0}}" class="no-more">
    <text>没有更多了</text>
  </view>
</view>
