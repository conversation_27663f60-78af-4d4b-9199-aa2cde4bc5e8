/* components/message-ui/message-ui.wxss */

.message-ui-container {
  width: 100%;
  background-color: #ffffff;
}

.message-list {
  width: 100%;
  padding-top: 16rpx;
  background-color: #ffffff;
}

.message-item {
  display: flex;
  background: #ffffff;
  padding: 32rpx 32rpx;
  position: relative;
  align-items: flex-start;
}

.message-item::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1px;
  background-color: #eee;
}

.message-item:last-child::after {
  display: none;
}

.message-item.highlighted {
  background-color: #f8f9fa;
}

.message-item:active {
  background-color: #f5f5f5;
}

.user-avatar {
  margin-right: 24rpx;
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
}

.unread-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid white;
}

.message-content {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.action-line {
  display: flex;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
  flex: 1;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
}

.comment-content {
  display: flex;
  align-items: flex-start;
  margin-top: 12rpx;
  margin-bottom: 12rpx;
}

.quote-mark {
  width: 4rpx;
  height: 1.4em;
  background-color: #ff6b6b;
  margin-right: 12rpx;
  border-radius: 2rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.comment-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  max-height: 3.2em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  flex: 1;
}

.message-text {
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.message-text text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.content-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.content-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.right-image {
  margin-left: 24rpx;
  flex-shrink: 0;
}

.content-thumb {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 260rpx 0;
  margin-top: -100rpx;
}

.empty-image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.empty-tip text {
  color: #999;
  font-size: 28rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  margin-top: 60rpx;
}

.loading-dots {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #999;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots.small {
  width: 30rpx;
  height: 30rpx;
  border-width: 3rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-more text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}

.no-more text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 展开历史按钮 */
.expand-history {
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.expand-btn {
  background: transparent;
  color: #999;
  border: none;
  border-radius: 0;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  font-weight: normal;
  box-shadow: none;
  line-height: 1.4;
}

.expand-btn:active {
  color: #666;
}

.expand-btn.loading {
  color: #ccc;
}

.expand-btn::after {
  border: none;
}
