/* 自定义学校弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.modal-container {
  width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

/* 弹窗头部 */
.modal-header {
  position: relative;
  padding: 32rpx 32rpx 16rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.close-icon {
  font-size: 32rpx;
  color: #999;
  line-height: 1;
}

/* 弹窗内容 */
.modal-content {
  padding: 32rpx;
}

.school-info {
  text-align: center;
  margin-bottom: 32rpx;
}

.school-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

.contact-section {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.contact-title {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.contact-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.contact-value {
  flex: 1;
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 600;
}

.copy-btn {
  padding: 8rpx 16rpx;
  background: #007AFF;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.copy-btn:active {
  transform: scale(0.95);
  background: #0056CC;
}

.copy-text {
  font-size: 22rpx;
  color: white;
  font-weight: 500;
}

/* 弹窗底部 */
.modal-footer {
  padding: 0 32rpx 32rpx;
}

.footer-btn {
  width: 100%;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.footer-btn:active {
  transform: scale(0.98);
  background: #e8e8e8;
}

.btn-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
