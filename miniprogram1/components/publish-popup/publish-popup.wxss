.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(3px);
}

.popup-show {
  visibility: visible;
  opacity: 1;
}

.popup-content {
  background-color: #ffffff;
  width: 100%;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 30rpx 30rpx 40rpx;
  position: relative;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.popup-show .popup-content {
  transform: translateY(0);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 36rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  text-align: center;
}

.popup-close {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  background-color: #f6f6f6;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.popup-close:active {
  transform: translateY(-50%) scale(0.95);
  background-color: #eeeeee;
}

.popup-close image {
  width: 30rpx;
  height: 30rpx;
}

/* 长条样式 */
.bar-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-radius: 18rpx;
  margin-bottom: 20rpx;
  margin-left: 0;
  margin-right: 0;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  background-size: 200% auto;
}

.bar-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.bar-item:first-child {
  background-color: #fff4d0;
  border: 1rpx solid rgba(234, 179, 8, 0.1);
}

.bar-item:nth-child(2) {
  background-color: #f0f9ff;
  border: 1rpx solid rgba(3, 105, 161, 0.1);
}

.bar-item:last-child {
  margin-bottom: 40rpx;
}

.bar-item:first-of-type {
  margin-top: 30rpx;
}

.bar-icon {
  width: 74rpx;
  height: 74rpx;
  border-radius: 14rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
}

.bar-item:first-child .bar-icon {
  background-color: #fff4d0;
}

.bar-item:nth-child(2) .bar-icon {
  background-color: #e0f2fe;
}

.bar-icon .bar-image {
  width: 40rpx;
  height: 40rpx;
}

.bar-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 550;
}

.bar-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  margin-right: 5rpx;
}

/* 图标样式 */
.option-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 36rpx;
  margin-top: 20rpx;
  padding: 0;
}

.option-item {
  width: 22%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;
}

.option-item:active {
  transform: scale(0.95);
}

.option-icon {
  width: 108rpx;
  height: 108rpx;
  border-radius: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
  transition: all 0.2s;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.option-icon .icon-image {
  width: 52rpx;
  height: 52rpx;
}

.option-text {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-top: 6rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
} 