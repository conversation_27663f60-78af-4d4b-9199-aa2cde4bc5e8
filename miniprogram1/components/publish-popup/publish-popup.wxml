<view class="popup-container {{visible ? 'popup-show' : ''}}" catchtap="onClose" catchtouchmove="preventTouchMove">
  <view class="popup-content" catchtap="stopPropagation">
    <view class="popup-header">
      <view class="popup-title">发布类型</view>
      <view class="popup-close" catchtap="onClose">
        <image src="/images/guanbi.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 外卖代取 - 长条 -->
    <view class="bar-item" bindtap="goToPublishPaotui" style="background-color: #fff4d0; border: 1rpx solid rgba(234, 179, 8, 0.1);">
      <view class="bar-icon" style="background-color: #fff4d0;">
        <image src="/images/paotuiren.png" mode="aspectFit" class="bar-image"></image>
      </view>
      <view class="bar-text">外卖代取</view>
      <view class="bar-arrow">
        <image src="/images/youjiantou-3.png" mode="aspectFit" style="width: 38rpx; height: 38rpx;"></image>
      </view>
    </view>
    
    <!-- 校园交易 - 长条 -->
    <view class="bar-item" bindtap="goToPublishErShou" style="background-color: #dcfce7; border: 1rpx solid rgba(22, 163, 74, 0.1); margin-bottom: 50rpx;">
      <view class="bar-icon" style="background-color: #dcfce7;">
        <image src="/images/ershouwupin.png" mode="aspectFit" class="bar-image"></image>
      </view>
      <view class="bar-text">校园交易</view>
      <view class="bar-arrow">
        <image src="/images/youjiantou-3.png" mode="aspectFit" style="width: 38rpx; height: 38rpx;"></image>
      </view>
    </view>
    
    <!-- 第一行选项（4个图标） -->
    <view class="option-row" style="margin-top: 20rpx;">
      <!-- 发条说说 -->
      <view class="option-item" bindtap="goToPublishWeituo">
        <view class="option-icon" style="background-color: #e0f2fe;">
          <image src="/images/dangshidati-01.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">发条说说</view>
      </view>
      
      <!-- 告白倾诉 -->
      <view class="option-item" bindtap="goToPublishQiuzhu">
        <view class="option-icon" style="background-color: #fff1f2;">
          <image src="/images/收藏.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">告白倾诉</view>
      </view>

      <!-- 寻找搭子 -->
      <view class="option-item" bindtap="goToPublishLianai">
        <view class="option-icon" style="background-color: #fef3c7;">
          <image src="/images/cansaitubiaozhuanqu-.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">寻找搭子</view>
      </view>

      <!-- 拼车交流 -->
      <view class="option-item" bindtap="goToPublishGuatian">
        <view class="option-icon" style="background-color: #f3e8ff;">
          <image src="/images/pinche.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">拼车交流</view>
      </view>
    </view>
    
    <!-- 第二行选项（4个图标） -->
    <view class="option-row">
      <!-- 失物寻找 -->
      <view class="option-item" bindtap="goToPublishJianzhi">
        <view class="option-icon" style="background-color: #fff1f2;">
          <image src="/images/shiwuzhaoling.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">失物寻找</view>
      </view>

      <!-- 租房求助 -->
      <view class="option-item" bindtap="goToPublishZulin">
        <view class="option-icon" style="background-color: #fef3c7;">
          <image src="/images/zufangyidianji.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">租房求助</view>
      </view>

      <!-- 房屋出租 -->
      <view class="option-item" bindtap="goToPublishChuzu">
        <view class="option-icon" style="background-color: #f3e8ff;">
          <image src="/images/zufang.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">房屋出租</view>
      </view>

      <!-- 赚点外快 -->
      <view class="option-item" bindtap="goToPublishXiaoyuan">
        <view class="option-icon" style="background-color: #e0f2fe;">
          <image src="/images/qujianzhi.png" mode="aspectFit" class="icon-image"></image>
        </view>
        <view class="option-text">赚点外快</view>
      </view>
    </view>
  </view>
</view> 