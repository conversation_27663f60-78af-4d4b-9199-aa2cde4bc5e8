// components/comment-input/comment-input.js
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    placeholder: {
      type: String,
      value: '写评论...'
    }
  },

  data: {
    content: '',
    selectedImages: [],
    focused: false,
    canSend: false
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.setData({
          focused: true
        })
      } else {
        this.setData({
          content: '',
          selectedImages: [],
          focused: false,
          canSend: false
        })
      }
    }
  },

  methods: {
    // 输入内容变化
    onInput(e) {
      const content = e.detail.value
      this.setData({
        content,
        canSend: content.trim().length > 0 || this.data.selectedImages.length > 0
      })
    },

    // 输入框获取焦点
    handleInputFocus() {
      this.setData({
        focused: true
      })
    },

    // 输入框失去焦点
    handleInputBlur() {
      // 延迟处理，避免点击工具栏时输入框失焦
      setTimeout(() => {
        this.setData({
          focused: false
        })
      }, 100)
    },

    // 选择图片
    chooseImage() {
      const maxImages = 3
      const currentCount = this.data.selectedImages.length

      if (currentCount >= maxImages) {
        wx.showToast({
          title: `最多只能选择${maxImages}张图片`,
          icon: 'none'
        })
        return
      }

      wx.chooseImage({
        count: maxImages - currentCount,
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: (res) => {
          this.uploadImages(res.tempFilePaths)
        }
      })
    },

    // 拍照
    takePhoto() {
      const maxImages = 3
      const currentCount = this.data.selectedImages.length

      if (currentCount >= maxImages) {
        wx.showToast({
          title: `最多只能选择${maxImages}张图片`,
          icon: 'none'
        })
        return
      }

      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera'],
        success: (res) => {
          this.uploadImages(res.tempFilePaths)
        }
      })
    },

    // 上传图片
    uploadImages(tempFilePaths) {
      wx.showLoading({ title: '上传中...' })

      const token = wx.getStorageSync('access_token')
      if (!token) {
        wx.hideLoading()
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        return
      }

      const uploadPromises = tempFilePaths.map(filePath => {
        return new Promise((resolve, reject) => {
          wx.uploadFile({
            url: getApp().globalData.wangz + '/upload/uploadToCos',
            filePath: filePath,
            name: 'file',
            header: {
              'token': token
            },
            formData: {
              'type': 'comment'
            },
            success: (res) => {
              try {
                const data = JSON.parse(res.data)
                if (data.code === 200) {
                  resolve(data.data.key) // 存储相对路径
                } else {
                  reject(new Error(data.msg || '上传失败'))
                }
              } catch (e) {
                reject(new Error('上传响应解析失败'))
              }
            },
            fail: reject
          })
        })
      })

      Promise.all(uploadPromises)
        .then(urls => {
          wx.hideLoading()
          const newImages = [...this.data.selectedImages, ...urls]
          this.setData({
            selectedImages: newImages,
            canSend: this.data.content.trim().length > 0 || newImages.length > 0
          })

          // 触发图片选择事件
          this.triggerEvent('imageSelected', {
            images: newImages
          })
        })
        .catch(error => {
          wx.hideLoading()
          console.error('上传图片失败:', error)
          wx.showToast({
            title: error.message || '上传失败',
            icon: 'none'
          })
        })
    },

    // 移除图片
    removeImage(e) {
      const index = e.currentTarget.dataset.index
      const newImages = this.data.selectedImages.filter((_, i) => i !== index)
      this.setData({
        selectedImages: newImages,
        canSend: this.data.content.trim().length > 0 || newImages.length > 0
      })

      // 触发图片选择事件
      this.triggerEvent('imageSelected', {
        images: newImages
      })
    },

    // 提交评论
    submit() {
      if (!this.data.canSend) {
        return
      }

      const content = this.data.content.trim()
      const images = this.data.selectedImages

      if (!content && images.length === 0) {
        wx.showToast({
          title: '请输入评论内容或选择图片',
          icon: 'none'
        })
        return
      }

      // 触发提交事件
      this.triggerEvent('submit', {
        content,
        images
      })
    },

    // 关闭评论输入框
    close() {
      this.triggerEvent('close')
    }
  }
})
