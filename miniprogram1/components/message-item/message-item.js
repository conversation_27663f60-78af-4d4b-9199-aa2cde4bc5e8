const { processAvatarUrl, processImageArray } = require('../../utils/imageUtil')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          // 确保ID和点赞数是数字类型，并处理图片URL
          const processedItem = {
            ...newVal,
            id: parseInt(newVal.id) || 0,
            total_likes: parseInt(newVal.total_likes) || 0,
            face_url: processAvatarUrl(newVal.face_url), // 处理头像URL
            images: processImageArray(newVal.images) // 处理图片数组
          };

          // 设置处理后的数据到组件的data中
          this.setData({
            processedItem: processedItem,
            targetId: processedItem.id,
            likeCount: processedItem.total_likes,
            isLiked: !!processedItem.is_liked
          });
        } else {
          // 如果没有数据，设置空的processedItem
          this.setData({
            processedItem: {
              face_url: '/images/weixiao.png',
              images: []
            }
          });
        }
      }
    },
    index: {
      type: Number,
      value: 0
    },
    abc: {
      type: Array,
      value: [254, 251, 229]
    },
    backgroundColor: {
      type: String,
      value: 'rgba(255, 255, 255, 0.8)' // 默认背景颜色
    },
    anonymousBackgroundColor: {
      type: String,
      value: 'rgba(240, 235, 225, 0.9)' // 默认匿名背景颜色
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    processedItem: {
      face_url: '/images/weixiao.png',
      images: []
    },
    targetId: 0,
    likeCount: 0,
    isLiked: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    viewMessageDetail: function() {
      // 触发父组件的事件
      this.triggerEvent('messagedetail', {
        item: this.properties.item,
        index: this.properties.index
      });
    },

    onLikeChange: function(e) {
      // 更新本地状态
      const { isLiked, totalLikes } = e.detail;
      this.setData({
        isLiked: isLiked,
        likeCount: parseInt(totalLikes) || 0
      });

      // 触发父组件的点赞事件
      this.triggerEvent('likechange', {
        ...e.detail,
        index: this.properties.index
      });
    },

    stopPropagation: function() {
      // 阻止事件冒泡
    }
  }
})
