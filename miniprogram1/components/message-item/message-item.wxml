<view class="num-item {{item.is_anonymous ? 'anonymous-item' : ''}}" bindtap="viewMessageDetail" style="background-color: {{item.is_anonymous ? anonymousBackgroundColor : backgroundColor}};">
  <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
    <view style="display: flex; align-items: center;">
      <image src="{{processedItem.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
      <view wx:if="{{!item.weizhi}}" style="display: flex; align-items: center;">
        <text style="margin-left:15rpx;">{{item.username}}</text>
        <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
          <view class="cyber-btn">
            匿名
            <view class="cyber-number">AN</view>
          </view>
        </view>
        <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
      </view>

      <!-- 如果 item.weizhi 不为空，显示这段代码 -->
      <view wx:if="{{item.weizhi}}" style="display: flex; flex-direction: column;">
        <view style="display: flex; align-items: center;">
          <text style="margin-left:15rpx;">{{item.username}}</text>
          <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
            <view class="cyber-btn">
              匿名
              <view class="cyber-number">AN</view>
            </view>
          </view>
          <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
        </view>
        <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{item.weizhi}}</text>
      </view>
    </view>
    <!-- 在最右边添加数据 -->
    <view>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '99'}}">寻找搭子</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '2'}}">发条说说</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '3'}}">校园交易</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '4'}}">告白倾诉</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '5'}}">拼车交流</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '6'}}">失物寻找</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '71'}}">房屋出租</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '72'}}">租房求助</text>
      <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '8'}}">赚点外快</text>
    </view>
  </view>
  <view class="text">{{item.content}}</view>
  <view wx:if="{{processedItem.images && processedItem.images.length > 0}}">
    <view class="image-container">
      <view wx:for="{{processedItem.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
        <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
      </view>
    </view>
  </view>
  <!-- 添加投票显示 -->
  <view wx:if="{{item.voteData}}" class="vote-container">
    <view class="divider"></view>
    <view class="vote-header">
      <view class="vote-title-row">
        <image src="/images/xingxingx.png" class="vote-title-icon"></image>
        <text class="vote-title">{{item.voteData.title}}</text>
        <view class="vote-type-tag">{{item.voteData.type === 'single' ? '单选' : '多选'}}</view>
      </view>
    </view>
  </view>
  <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
    <view class="gradient-text" style="line-height: 50rpx;">{{item.send_timestamp}}</view>
    <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
      <view class="last-item" style="color: rgb(226, 114, 23);">{{item.jine}}</view>
      <view class="action-item" catchtap="stopPropagation">
        <image src="/images/pinglun2.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
        <view class="last-item" style="margin-left: 10rpx;">{{item.total_pinglun}}</view>
      </view>
      <view class="action-item" catchtap="stopPropagation">
        <like-button
          type="message"
          targetId="{{targetId}}"
          isLiked="{{isLiked}}"
          likeCount="{{likeCount}}"
          size="normal"
          custom-class="home-like-btn"
          bind:likechange="onLikeChange"
          data-index="{{index}}"
        />
      </view>
    </view>
  </view>
</view>
