/* 文字框样式 */
.num-item, .num-item2 {
  box-sizing: border-box;
  border-radius: 34rpx;
  margin: 35rpx;
  padding: 20rpx 25rpx 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  width: 90%;
  /* 背景颜色通过内联样式控制 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: auto;
  height: auto !important;
  position: relative;
  z-index: 1;
}

/* 第一个消息项的特殊上边距 */
.num-item:first-of-type {
  margin-top: 10rpx;
}

/* 匿名帖子样式 - 只保留边框和阴影，背景颜色通过内联样式控制 */
.num-item.anonymous-item {
  border-left: 4rpx solid rgba(180, 160, 140, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.text {
  font-size: 32rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: pre-wrap;
  padding: 0;
}

.touxiang1 {
  margin-bottom: 15rpx;
  height: 70rpx;
  display: flex;
  background-color: transparent;
  border: 3rpx;
  align-items: flex-start;
}

.image-container {
  margin-top: 8rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.image-item {
  flex: 1;
  min-width: 0;
  max-width: calc(33.333% - 6rpx);
}

.uniform-image {
  width: 100%;
  height: 198rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.kuang {
  height: 60rpx;
  margin-top: auto;
  display: flex;
  align-items: center;
}

.last {
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 35rpx;
  line-height: 35rpx;
}

.action-item {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
  position: relative;
  z-index: 2;
}

.action-item image {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
}

.action-item view {
  margin-left: 10rpx;
  color: #000000;
  font-size: 25rpx;
}

.gradient-text {
  font-size: 24rpx;
  color: #808080;
  line-height: 35rpx;
}

.last image {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

.last .last-item {
  margin-left: 10rpx;
  line-height: 35rpx;
}

/* 头衔样式 */
.touxian{
  padding: 7rpx;
  margin-left:15rpx;
  background-image: linear-gradient(to left, #f64f59, #c471ed, #12c2e9);
  color: white;
  font-size: 20rpx;
  border-radius: 8rpx;
  line-height: 1
}
.touxian2{
  padding: 7rpx;
  margin-left:15rpx;
  color: white;
  font-size: 20rpx;
  border-radius: 8rpx;
  line-height: 1;
  display: inline-block;
}

/* 投票样式 */
.vote-container {
  margin: 0;
  padding: 0;
  background-color: transparent;
  position: relative;
}

.vote-header {
  margin-bottom: 0;
}

.vote-title-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
  width: fit-content;
  max-width: 100%;
}

.vote-title-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

.vote-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400rpx;
}

.vote-type-tag {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 1.2;
  height: fit-content;
  margin-left: auto;
}

.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 10rpx 0;
  margin-top: 20rpx;
}

/* 通用赛博朋克风格标签 - 参考messageDetail页面的帖主标签 */
.cyber-badge {
  position: relative;
  height: 28rpx;
  width: 64rpx;
  margin-left: 10rpx;
  display: inline-block;
}

.cyber-btn {
  --primary: #ff184c;
  --shadow-primary: #fded00;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  color: var(--color);
  text-transform: uppercase;
  font-size: 16rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 28rpx;
  text-align: center;
}

.cyber-btn::after, .cyber-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
  z-index: -1;
}

.cyber-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.cyber-btn::after {
  background: var(--primary);
}

.cyber-number {
  background: var(--shadow-primary);
  color: #323232;
  font-size: 10rpx;
  font-weight: 700;
  letter-spacing: 0.1rpx;
  position: absolute;
  width: 20rpx;
  height: 8rpx;
  top: 0;
  left: 78%;
  line-height: 8rpx;
  text-align: center;
}

/* 头衔背景颜色类 */
.bg0 {
  background-color: #CED5E9; /* 主背景颜色 */
}
.bg1 {
  background-color: #e57373; /* 红色 */
}
.bg2 {
  background-color: #98E9E3; /* 青色 */
}
.bg3 {
  background-color: #ff8a65; /* 橙色 */
}
.bg4 {
  background-color: #E6C9F6; /* 淡紫色背景 */
}
.bg5 {
  background-color: #7986cb; /* 蓝紫 */
}
.bg6 {
  background-color: #64b5f6; /* 浅蓝 */
}
.bg7 {
  background-color: #81c784; /* 浅绿 */
}
.bg8 {
  background: linear-gradient(to right, #7AA1D2, #f5c3c3, #CC95C0); /* 渐变背景 */
}
.bg9 {
  background: linear-gradient(to right, #ffc0cb, #800080); /* 渐变背景 */
}
.bg10 {
  background-image: linear-gradient(to left, #f64f59, #c471ed, #12c2e9);
}
