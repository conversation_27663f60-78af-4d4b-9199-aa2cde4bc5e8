<!-- 普通微信群提示 -->
<view class="group-notice num-item2" wx:if="{{show && !isTrading}}" bindtap="showQRCodeModal">
  <view class="notice-content">
    <image class="group-avatar" src="/images/vx.jpg" mode="aspectFill"></image>
    <view class="notice-text">
      <view class="notice-title">点击进入树洞消息推送群</view>
      <view class="notice-desc">浏览校园互动(右上角可关闭)</view>
    </view>
  </view>
  <view class="close-btn" catchtap="onShowModal">
    <image src="/images/guanbi.png" mode="aspectFit"></image>
  </view>
</view>

<!-- 二手物品快速交易群提示 -->
<view class="trading-group-notice num-item2" wx:if="{{show && isTrading}}" bindtap="showQRCodeModal">
  <view class="trading-content">
    <view class="trading-icon-container">
      <view class="trading-icon">🛒</view>
    </view>
    <view class="trading-text">
      <view class="trading-title">二手物品快速交易群！</view>
      <view class="trading-desc">只推送低价物品，快来甩卖或捡漏吧</view>
    </view>
  </view>
  <view class="close-btn trading-close-btn" catchtap="onShowModal">
    <image src="/images/guanbi.png" mode="aspectFit"></image>
  </view>
</view>

<!-- 扫码加入微信群的弹窗 -->
<view wx:if="{{showQRCode}}" class="modal-mask" bindtap="closeQRCodeModal">
  <view class="qrcode-modal" catchtap="stopPropagation">
    <view class="modal-close-btn" bindtap="closeQRCodeModal">
      <image src="/images/guanbi.png" mode="aspectFit"></image>
    </view>
    <view class="qrcode-title">{{isTrading ? '长按扫码加入二手物品快速交易群' : '长按扫码加入微信推送群'}}</view>
    <image class="qrcode-image" src="{{qrcodeImageUrl}}" mode="widthFix" show-menu-by-longpress="true"></image>
  </view>
</view>

<!-- 美化后的自定义弹窗，始终在最顶层 -->
<view wx:if="{{showModal}}" class="modal-mask" bindtap="onCloseModal">
  <view class="card-modal" catchtap="stopPropagation">
    <!-- 关闭按钮，确保明显可见 -->
    <view class="modal-close-btn" bindtap="onCloseModal">
      <image src="/images/guanbi.png" mode="aspectFit"></image>
    </view>
    
    <view class="modal-header">
      <image class="modal-icon-square" src="{{isTrading ? '/images/ershouwupin.png' : '/images/vx.jpg'}}" mode="aspectFill"></image>
      <view class="modal-title">{{isTrading ? '关闭二手物品快速交易群提示' : '关闭微信群提示'}}</view>
    </view>
    <view class="modal-message">
      {{isTrading ? '您关闭二手物品快速交易群提示后可以在个人中心-设置里重新开启' : '您关闭微信群提示后可以在个人中心-设置里开启，或者在主页点击右侧微信图标加入'}}
    </view>
    <button class="modal-main-btn" bindtap="onConfirmClose">确认关闭</button>
  </view>
</view> 