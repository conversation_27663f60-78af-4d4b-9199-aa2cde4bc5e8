Component({
  properties: {
    // 可以添加需要的属性
    choose: {
      type: Number,
      value: 0
    }
  },

  data: {
    show: true,
    showModal: false,
    showQRCode: false,
    qrcodeImageUrl: '',
    isTrading: false,
    buttonType: 'group_notice',
    storageKey: 'showGroupNotice'
  },

  observers: {
    'choose': function(newVal) {
      // 当choose参数变化时重新初始化组件
      this.initializeComponent();
    }
  },

  lifetimes: {
    // 在组件实例刚刚被创建时执行
    created() {
      this.initializeComponent();
    },

    // 在组件实例进入页面节点树时执行
    attached() {
      this.initializeComponent();
      // 从服务器获取最新的按钮状态
      this.checkServerSetting();
      // 更新二维码图片URL
      this.updateQRCodeImage();

      // 延迟检查choose参数，确保父页面数据已加载
      setTimeout(() => {
        if (this.data.choose !== this.properties.choose) {
          this.initializeComponent();
        }
      }, 500);
    }
  },

  pageLifetimes: {
    // 页面被展示时触发
    show() {
      // 页面显示时也检查状态，确保实时响应设置变化
      const storageValue = wx.getStorageSync(this.data.storageKey);
      if (storageValue === false) {
        this.setData({ show: false });
      }

      // 每次页面显示时更新二维码图片URL，确保获取最新图片
      this.updateQRCodeImage();
    }
  },

  methods: {
    // 初始化组件
    initializeComponent() {
      const choose = this.properties.choose || 0;
      const isTrading = choose === 3;

      let storageKey, buttonType;

      // 根据choose参数设置不同的配置
      if (isTrading) {
        storageKey = 'showTradingGroupNotice';
        buttonType = 'trading_group_notice';
      } else {
        storageKey = 'showGroupNotice';
        buttonType = 'group_notice';
      }

      // 检查本地存储状态
      const storageValue = wx.getStorageSync(storageKey);

      // 设置组件数据 - 对于二手物品快速交易群，如果本地存储为空，默认显示
      let shouldShow;
      if (isTrading) {
        // 二手物品快速交易群：只有明确设置为false才不显示，否则默认显示
        shouldShow = storageValue !== false;
      } else {
        // 普通微信群：按原逻辑处理
        shouldShow = storageValue !== false;
      }

      this.setData({
        isTrading: isTrading,
        buttonType: buttonType,
        storageKey: storageKey,
        show: shouldShow
      });



      // 初始化二维码图片URL
      this.updateQRCodeImage();
    },

    // 更新二维码图片URL，添加时间戳防止缓存
    updateQRCodeImage() {
      const timestamp = new Date().getTime();
      let qrcodeImageUrl;

      if (this.data.isTrading) {
        qrcodeImageUrl = `https://www.bjgaoxiaoshequ.store/tupian/trading_group.jpg?t=${timestamp}`;
      } else {
        qrcodeImageUrl = `https://www.bjgaoxiaoshequ.store/tupian/微信群1.jpg?t=${timestamp}`;
      }

      this.setData({ qrcodeImageUrl });
    },

    // 检查服务器上的设置状态
    checkServerSetting() {
      // 获取用户ID
      const userId = getApp().globalData.user_id;
      if (!userId) return;

      wx.request({
        url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
        method: 'POST',
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token')
        },
        data: {
          user_id: userId,
          button_type: this.data.buttonType
        },
        success: (res) => {
          if (res.data && res.data.code === 200) {
            const show = res.data.data.button_status === '1';
            this.setData({ show: show });
            // 同步到本地存储
            wx.setStorageSync(this.data.storageKey, show);
          } else {
            // 如果服务器返回错误，对于二手物品快速交易群保持默认显示
            if (this.data.isTrading) {
              this.setData({ show: true });
              wx.setStorageSync(this.data.storageKey, true);
            }
          }
        },
        fail: (err) => {
          // 如果请求失败，对于二手物品快速交易群，保持默认显示状态
          if (this.data.isTrading) {
            this.setData({ show: true });
            wx.setStorageSync(this.data.storageKey, true);
          }
        }
      });
    },

    showQRCodeModal() {
      // 每次显示弹窗时更新图片URL，确保获取最新图片
      this.updateQRCodeImage();
      this.setData({ showQRCode: true });
    },
    
    closeQRCodeModal() {
      this.setData({ showQRCode: false });
    },
    
    onShowModal() {
      this.setData({ showModal: true })
    },
    
    onCloseModal() {
      this.setData({ showModal: false })
    },
    
    stopPropagation() {
      // 阻止事件冒泡
      return false
    },
    
    onConfirmClose() {
      // 请求后端接口关闭群提示
      const toastTitle = this.data.isTrading ? '已关闭二手物品快速交易群提示' : '已关闭微信群提示';

      wx.request({
        url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
        method: 'POST',
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token')
        },
        data: {
          user_id: getApp().globalData.user_id,
          button_type: this.data.buttonType,
          button_status: '0'
        },
        success: () => {
          wx.setStorageSync(this.data.storageKey, false);
          this.setData({ show: false, showModal: false });
          wx.showToast({
            title: toastTitle,
            icon: 'none'
          });
        },
        fail: () => {
          wx.showToast({ title: '网络错误', icon: 'none' });
        }
      })
    }
  }
}) 