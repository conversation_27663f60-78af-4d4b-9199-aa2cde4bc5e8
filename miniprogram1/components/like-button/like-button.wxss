/* components/like-button/like-button.wxss */

.like-button {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* 尺寸变体 */
.like-button.size-small {
  gap: 6rpx;
}

.like-button.size-normal {
  gap: 10rpx;
}

.like-button.size-large {
  gap: 14rpx;
}

/* 点赞图标 */
.like-icon {
  transition: transform 0.2s ease;
}

.like-button.size-small .like-icon {
  width: 28rpx;
  height: 28rpx;
}

.like-button.size-normal .like-icon {
  width: 35rpx;
  height: 35rpx;
}

.like-button.size-large .like-icon {
  width: 42rpx;
  height: 42rpx;
}

/* 点赞数量 */
.like-count {
  font-size: 25rpx;
  color: #333;
  min-width: 20rpx;
  text-align: center;
}

.like-button.size-small .like-count {
  font-size: 22rpx;
}

.like-button.size-large .like-count {
  font-size: 28rpx;
}

/* 已点赞状态 - 数字保持黑色，不跟随点赞状态变色 */

/* 点击效果 */
.like-button:active:not(.disabled):not(.loading) {
  transform: scale(0.95);
}

.like-button:active:not(.disabled):not(.loading) .like-icon {
  transform: scale(1.1);
}

/* 禁用状态 */
.like-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.like-button.disabled .like-icon,
.like-button.disabled .like-count {
  pointer-events: none;
}

/* 加载状态 */
.like-button.loading {
  pointer-events: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: inherit;
}

.loading-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #f3f3f3;
  border-top: 2rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 点赞动画效果 */
.like-button.liked .like-icon {
  animation: likeAnimation 0.3s ease;
}

@keyframes likeAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .like-button.size-normal .like-icon {
    width: 32rpx;
    height: 32rpx;
  }
  
  .like-button.size-normal .like-count {
    font-size: 24rpx;
  }
}
