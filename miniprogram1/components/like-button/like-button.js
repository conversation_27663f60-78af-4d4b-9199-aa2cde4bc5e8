/**
 * 通用点赞按钮组件
 * 支持帖子、评论、回复等所有类型的点赞
 */

const { doLike, LIKE_TYPES } = require('../../utils/like-util.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 点赞类型
    type: {
      type: String,
      value: LIKE_TYPES.MESSAGE
    },
    // 目标ID
    targetId: {
      type: Number,
      value: 0
    },
    // 帖子ID（评论和回复点赞时需要）
    messageId: {
      type: Number,
      value: 0
    },
    // 评论ID（回复点赞时需要）
    commentId: {
      type: Number,
      value: 0
    },
    // 是否已点赞
    isLiked: {
      type: Boolean,
      value: false
    },
    // 点赞数量
    likeCount: {
      type: Number,
      value: 0
    },
    // 按钮大小
    size: {
      type: String,
      value: 'normal' // normal, small, large
    },
    // 是否显示点赞数
    showCount: {
      type: Boolean,
      value: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: false,
    // 内部状态，用于乐观更新
    currentIsLiked: false,
    currentLikeCount: 0,
    // 标记是否已初始化
    initialized: false,
    // 无感防抖标记
    debouncing: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件状态
     */
    initializeState() {
      if (!this.data.initialized) {
        this.setData({
          currentIsLiked: this.properties.isLiked,
          currentLikeCount: this.properties.likeCount,
          initialized: true
        })
      }
    },

    /**
     * 点赞按钮点击事件
     */
    onLikeClick() {
      // 无感防抖：如果正在防抖中，直接返回，不显示任何loading效果
      if (this.data.debouncing || this.properties.disabled) {
        return
      }

      // 确保状态已初始化
      this.initializeState()

      // 乐观更新：先更新UI状态
      const originalIsLiked = this.data.currentIsLiked
      const originalLikeCount = this.data.currentLikeCount
      const newIsLiked = !originalIsLiked
      const newLikeCount = originalLikeCount + (newIsLiked ? 1 : -1)

      // 立即更新组件内部状态（乐观更新）
      this.setData({
        currentIsLiked: newIsLiked,
        currentLikeCount: newLikeCount
      })

      // 立即触发父组件事件
      this.triggerEvent('likechange', {
        type: this.properties.type,
        targetId: this.properties.targetId,
        messageId: this.properties.messageId,
        commentId: this.properties.commentId,
        isLiked: newIsLiked,
        totalLikes: newLikeCount
      })

      // 无感防抖：设置防抖标记，但不显示loading
      this.setData({ debouncing: true })
      setTimeout(() => {
        this.setData({ debouncing: false })
      }, 500)

      // 执行点赞（后台处理）
      doLike({
        type: this.properties.type,
        targetId: this.properties.targetId,
        messageId: this.properties.messageId,
        commentId: this.properties.commentId,
        onSuccess: (result) => {
          // 成功后用服务器返回的真实数据更新
          this.setData({
            currentIsLiked: result.isLiked,
            currentLikeCount: result.totalLikes
          })

          if (result.isLiked !== newIsLiked || result.totalLikes !== newLikeCount) {
            // 如果服务器返回的状态与乐观更新不一致，再次触发事件
            this.triggerEvent('likechange', {
              type: this.properties.type,
              targetId: this.properties.targetId,
              messageId: this.properties.messageId,
              commentId: this.properties.commentId,
              isLiked: result.isLiked,
              totalLikes: result.totalLikes
            })
          }
        },
        onError: (error) => {
          // 失败时回滚到原始状态
          this.setData({
            currentIsLiked: originalIsLiked,
            currentLikeCount: originalLikeCount
          })

          this.triggerEvent('likechange', {
            type: this.properties.type,
            targetId: this.properties.targetId,
            messageId: this.properties.messageId,
            commentId: this.properties.commentId,
            isLiked: originalIsLiked,
            totalLikes: originalLikeCount
          })
          this.handleLikeError(error)
        }
      })
    },



    /**
     * 处理点赞失败
     */
    handleLikeError(error) {
      console.error('点赞失败:', error)
      
      // 触发错误事件
      this.triggerEvent('likeerror', {
        type: this.properties.type,
        targetId: this.properties.targetId,
        error: error
      })
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation(e) {
      // 阻止事件冒泡，避免触发父元素的点击事件
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
      this.initializeState()
    },

    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },

  /**
   * 监听器 - 当properties变化时同步到内部状态
   */
  observers: {
    'isLiked, likeCount': function(isLiked, likeCount) {
      // 只有在组件初始化后才同步外部状态变化
      if (this.data.initialized) {
        this.setData({
          currentIsLiked: isLiked,
          currentLikeCount: likeCount
        })
      }
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
    },

    hide() {
      // 组件所在的页面被隐藏时执行
    }
  }
})
