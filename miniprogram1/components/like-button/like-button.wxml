<!--components/like-button/like-button.wxml-->
<view class="like-button {{customClass}} size-{{size}} {{currentIsLiked ? 'liked' : 'unliked'}} {{disabled ? 'disabled' : ''}} {{loading ? 'loading' : ''}}"
      catchtap="onLikeClick">

  <!-- 点赞图标 -->
  <image
    class="like-icon"
    src="{{currentIsLiked ? '/images/icon-2.png' : '/images/icon.png'}}"
    mode="aspectFit"
  />

  <!-- 点赞数量 -->
  <text wx:if="{{showCount}}" class="like-count">{{currentLikeCount || 0}}</text>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
  </view>
</view>
