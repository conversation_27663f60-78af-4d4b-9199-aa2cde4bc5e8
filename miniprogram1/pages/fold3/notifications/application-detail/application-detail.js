const app = getApp();
const { processImageUrl, getBucketTypeFromPath } = require('../../../../utils/imageUtil');

Page({
  data: {
    id: null,
    application: null,
    reason: ''
  },

  onLoad(options) {
    const id = Number(options.id || 0);
    this.setData({ id });
    if (id) {
      this.fetchDetail(id);
    } else {
      wx.showToast({ title: '缺少申请ID', icon: 'none' });
    }
  },

  onClickLeft() {
    wx.navigateBack();
  },

  // 预览认证图片
  previewImage() {
    const { application } = this.data;
    if (!application || !application.image_url) return;

    const bucketType = getBucketTypeFromPath(application.image_url);
    const fullUrl = processImageUrl(application.image_url, '', bucketType);

    wx.previewImage({
      current: fullUrl,
      urls: [fullUrl]
    });
  },

  fetchDetail(id) {
    wx.showLoading({ title: '加载中', mask: true });
    wx.request({
      url: `${app.globalData.wangz}/auth/getApplicationDetail`,
      method: 'GET',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token'),
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      data: {
        id,
        _t: Date.now() // 添加时间戳防止缓存
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const appData = res.data.data || {};

          // 统一补全图片URL（支持COS相对路径）
          const bucketType = getBucketTypeFromPath(appData.image_url || '');
          appData.image_url = processImageUrl(appData.image_url || '', '', bucketType);
          this.setData({ application: appData });
        } else {
          wx.showToast({ title: res.data.msg || '加载失败', icon: 'none' });
        }
      },
      fail: () => wx.showToast({ title: '网络错误', icon: 'none' }),
      complete: () => wx.hideLoading()
    });
  },

  onReasonInput(e) {
    this.setData({ reason: e.detail.value });
  },

  onApprove() {
    this.review('approve');
  },

  onReject() {
    this.review('reject');
  },

  review(action) {
    const { id, reason } = this.data;
    if (!id) return;

    wx.showLoading({ title: '提交中', mask: true });
    wx.request({
      url: `${app.globalData.wangz}/auth/reviewApplication`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': wx.getStorageSync('access_token')
      },
      data: { application_id: id, action, reason },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          wx.showToast({ title: '操作成功', icon: 'success' });
          // 延迟重新拉取详情，确保后端事务已提交
          setTimeout(() => {
            this.fetchDetail(id);
          }, 500);
        } else {
          wx.showToast({ title: res.data.msg || '操作失败', icon: 'none' });
        }
      },
      fail: () => wx.showToast({ title: '网络错误', icon: 'none' }),
      complete: () => wx.hideLoading()
    });
  }
});
