<view class="detail-container">
  <custom-nav-bar title="认证申请详情" bind:left-click="onClickLeft"></custom-nav-bar>

  <view class="content" wx:if="{{application}}">
    <!-- 顶部卡片 -->
    <view class="card">
      <view class="card-row"><text class="label">用户</text><text class="value">{{application.username}}（ID: {{application.user_id}}）</text></view>
      <view class="card-row"><text class="label">学校</text><text class="value">{{application.school_name}}（ID: {{application.school_id}}）</text></view>
      <view class="card-row"><text class="label">申请时间</text><text class="value">{{application.created_at}}</text></view>
      <view class="card-row"><text class="label">状态</text><text class="value status {{application.status}}">{{application.status}}</text></view>
    </view>

    <!-- 图片卡片 -->
    <view class="card">
      <view class="card-title">认证图片</view>
      <image class="auth-image" mode="widthFix" src="{{application.image_url}}" show-menu-by-longpress="true" bindtap="previewImage"></image>
      <view class="tips">点击图片可预览</view>
    </view>

    <!-- 审核区 -->
    <view class="card" wx:if="{{application.status==='pending'}}">
      <view class="card-title">审核处理</view>
      <textarea class="reason" placeholder="可填写备注或拒绝原因（可选，最多200字）" maxlength="200" bindinput="onReasonInput" value="{{reason}}" />
      <view class="actions">
        <button class="btn reject" bindtap="onReject">拒绝</button>
        <button class="btn approve" bindtap="onApprove">通过</button>
      </view>
    </view>

    <!-- 结果区 -->
    <view class="card" wx:if="{{application.status!=='pending'}}">
      <view class="card-title">审核结果</view>
      <view class="card-row"><text class="label">审核人</text><text class="value">{{application.reviewed_by || '—'}}</text></view>
      <view class="card-row"><text class="label">审核时间</text><text class="value">{{application.reviewed_at || '—'}}</text></view>
      <view class="card-row"><text class="label">备注</text><text class="value">{{application.reason || '—'}}</text></view>
    </view>
  </view>

  <view wx:else class="empty">正在加载...</view>
</view>

