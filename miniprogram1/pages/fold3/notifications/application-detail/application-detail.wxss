.detail-container { padding-bottom: 24rpx; background: #f6f7fb; min-height: 100vh; }
.content { padding: 0 24rpx 24rpx 24rpx; }

/* 卡片样式，参考其他页面UI */
.card { background: #fff; border-radius: 16rpx; box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.04); padding: 24rpx; margin-bottom: 24rpx; }
.card-title { font-size: 30rpx; font-weight: 600; color: #333; margin-bottom: 16rpx; }
.content .card:first-child { margin-top: 0; }

.card-row { display: flex; align-items: baseline; margin-bottom: 12rpx; }
.card-row:last-child { margin-bottom: 0; }
.label { color: #8c8c8c; width: 160rpx; font-size: 26rpx; }
.value { color: #2c2c2c; font-size: 28rpx; }
.status.pending { color: #e6a23c; }
.status.approved { color: #07c160; }
.status.rejected { color: #ee0a24; }

.auth-image { width: 100%; border-radius: 12rpx; background: #f7f7f7; }
.tips { margin-top: 8rpx; color: #9c9c9c; font-size: 24rpx; }

.reason { width: 100%; min-height: 160rpx; padding: 16rpx; border: 1px solid #eee; border-radius: 12rpx; box-sizing: border-box; background: #fafafa; }
.actions { display: flex; justify-content: space-between; margin-top: 16rpx; }
.btn { flex: 1; margin: 0 8rpx; height: 88rpx; line-height: 88rpx; border-radius: 12rpx; color: #fff; font-size: 30rpx; }
.btn.approve { background: #07c160; }
.btn.reject { background: #ee0a24; }

.empty { padding: 48rpx; text-align: center; color: #888; }
