<?php
declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 用户角色验证器
 */
class UserRoleValidate extends Validate
{
    protected $rule = [
        'target_user_id' => 'require|integer|gt:0',
        'role_type' => 'require|in:group_owner,group_admin,official_account_admin,club_admin,business_admin,institution_admin',
        'target_id' => 'require|integer|gt:0',
        'target_type' => 'require|in:group,organization,official_account,business,institution',
        'expires_at' => 'dateFormat:Y-m-d H:i:s'
    ];

    protected $message = [
        'target_user_id.require' => '目标用户ID不能为空',
        'target_user_id.integer' => '目标用户ID必须是整数',
        'target_user_id.gt' => '目标用户ID必须大于0',
        'role_type.require' => '角色类型不能为空',
        'role_type.in' => '角色类型不正确',
        'target_id.require' => '目标ID不能为空',
        'target_id.integer' => '目标ID必须是整数',
        'target_id.gt' => '目标ID必须大于0',
        'target_type.require' => '目标类型不能为空',
        'target_type.in' => '目标类型不正确',
        'expires_at.dateFormat' => '过期时间格式不正确'
    ];

    protected $scene = [
        'grant' => ['target_user_id', 'role_type', 'target_id', 'target_type', 'expires_at'],
        'revoke' => ['target_user_id', 'role_type', 'target_id', 'target_type']
    ];
}
