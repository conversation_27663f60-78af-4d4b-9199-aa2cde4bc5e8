<?php
namespace app\validate;

use think\Validate;

class UserValidate extends Validate
{
    protected $rule = [
        'username' => 'require|length:1,20|checkUsername',
        'user_id' => 'require|integer|gt:0',
        'titlename' => 'length:0,10',
        'titlecolor' => 'length:0,20',
    ];

    protected $message = [
        'username.require' => '用户名不能为空',
        'username.length' => '用户名长度必须在1-20个字符之间',
        'username.checkUsername' => '用户名包含非法字符',
        'user_id.require' => '用户ID不能为空',
        'user_id.integer' => '用户ID必须是整数',
        'user_id.gt' => '用户ID必须大于0',
        'titlename.length' => '头衔长度不能超过10个字符',
        'titlecolor.length' => '头衔颜色长度不能超过20个字符',
    ];

    /**
     * 自定义验证用户名
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    protected function checkUsername($value, $rule, $data)
    {
        // 检查是否包含SQL注入关键词
        $sqlKeywords = [
            'select', 'insert', 'update', 'delete', 'drop', 'union', 
            'script', 'javascript', 'exec', 'cmd', 'eval', 'or', 'and'
        ];
        
        $lowerValue = strtolower($value);
        foreach ($sqlKeywords as $keyword) {
            if (stripos($lowerValue, $keyword) !== false) {
                return '用户名不能包含SQL关键词';
            }
        }
        
        // 检查真正危险的字符（允许一些安全的特殊字符如#、&、@等）
        $dangerousChars = ["'", '"', ';', '--', '/*', '*/', '<', '>', '\\', 'script'];
        foreach ($dangerousChars as $char) {
            if (stripos($value, $char) !== false) {
                return '用户名不能包含危险字符';
            }
        }
        
        // 检查SQL注入模式
        $patterns = [
            '/\bunion\s+select\b/i',
            '/\bor\s+1\s*=\s*1\b/i',
            '/\'\s*or\s*\'/i',
            '/--\s*$/',
            '/\/\*.*?\*\//',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return '用户名格式不正确';
            }
        }
        
        // 允许中文、英文、数字、下划线和一些安全的特殊字符
        if (!preg_match('/^[\x{4e00}-\x{9fa5}a-zA-Z0-9_#&@.+-]+$/u', $value)) {
            return '用户名只能包含中文、英文、数字、下划线和部分特殊字符(#&@.+-)';
        }
        
        return true;
    }
}
