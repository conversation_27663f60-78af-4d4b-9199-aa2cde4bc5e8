<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use think\facade\Log;
use app\util\PermissionHelper;
use app\util\JwtUtil;
use app\service\UidService;

/**
 * UID管理控制器
 */
class UidManagement extends BaseController
{
    /**
     * 获取学校UID统计信息
     */
    public function getSchoolStats(Request $request)
    {
        // 验证token和管理员权限
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        try {
            $payload = JwtUtil::validateToken($token);
            if (!$payload || !PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }

            $schoolId = $request->param('school_id', 0);
            
            if ($schoolId) {
                // 获取指定学校的统计信息
                $stats = UidService::getSchoolUidStats($schoolId);
                if (empty($stats)) {
                    return json(['code' => 404, 'msg' => '学校不存在或数据获取失败']);
                }
                
                // 获取学校信息
                $school = Db::table('beijing_universities')
                    ->where('university_id', $schoolId)
                    ->find();
                
                $stats['school_info'] = $school;
                
                return json([
                    'code' => 200,
                    'msg' => '获取成功',
                    'data' => $stats
                ]);
            } else {
                // 获取所有学校的统计信息
                $schools = Db::table('beijing_universities')
                    ->field('university_id, university_name, short_name')
                    ->where('is_active', 1)
                    ->select()
                    ->toArray();
                
                $allStats = [];
                foreach ($schools as $school) {
                    $stats = UidService::getSchoolUidStats($school['university_id']);
                    if (!empty($stats)) {
                        $stats['school_info'] = $school;
                        $allStats[] = $stats;
                    }
                }
                
                return json([
                    'code' => 200,
                    'msg' => '获取成功',
                    'data' => $allStats
                ]);
            }

        } catch (\Exception $e) {
            Log::error('获取学校UID统计失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '服务器错误']);
        }
    }

    /**
     * 批量为已认证用户分配UID（数据迁移用）
     */
    public function batchAssignUids(Request $request)
    {
        // 验证token和管理员权限
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        try {
            $payload = JwtUtil::validateToken($token);
            if (!$payload || !PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }

            // 执行批量分配
            $stats = UidService::batchAssignUids();
            
            return json([
                'code' => 200,
                'msg' => '批量分配完成',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('批量分配UID失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '服务器错误']);
        }
    }

    /**
     * 查询用户UID信息
     */
    public function getUserUid(Request $request)
    {
        // 验证token和管理员权限
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        try {
            $payload = JwtUtil::validateToken($token);
            if (!$payload || !PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }

            $userId = $request->param('user_id', 0);
            if (!$userId) {
                return json(['code' => 400, 'msg' => '请提供用户ID']);
            }

            // 查询用户信息
            $user = Db::table('user')
                ->alias('u')
                ->leftJoin('beijing_universities bu', 'u.verified_university_id = bu.university_id')
                ->field('u.id, u.username, u.status, u.verified_university_id, u.uid, bu.university_name, bu.short_name')
                ->where('u.id', $userId)
                ->find();

            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $user
            ]);

        } catch (\Exception $e) {
            Log::error('查询用户UID失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '服务器错误']);
        }
    }

    /**
     * 手动为用户分配UID
     */
    public function assignUidToUser(Request $request)
    {
        // 验证token和管理员权限
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        try {
            $payload = JwtUtil::validateToken($token);
            if (!$payload || !PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }

            $userId = $request->param('user_id', 0);
            $universityId = $request->param('university_id', 0);

            if (!$userId || !$universityId) {
                return json(['code' => 400, 'msg' => '请提供用户ID和学校ID']);
            }

            // 分配UID
            $uid = UidService::assignUidToUser($userId, $universityId);
            
            if ($uid) {
                return json([
                    'code' => 200,
                    'msg' => 'UID分配成功',
                    'data' => [
                        'user_id' => $userId,
                        'university_id' => $universityId,
                        'uid' => $uid
                    ]
                ]);
            } else {
                return json(['code' => 500, 'msg' => 'UID分配失败']);
            }

        } catch (\Exception $e) {
            Log::error('手动分配UID失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '服务器错误']);
        }
    }
}
