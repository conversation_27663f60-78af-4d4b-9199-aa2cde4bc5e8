<?php
namespace app\controller\common;

use think\Response;

/**
 * API基础控制器
 * 提供统一的API响应格式
 */
abstract class BaseApiController extends BaseController
{
    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应码
     * @return Response
     */
    protected function success($data = null, $message = 'success', $code = 200)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        return json($response);
    }

    /**
     * 失败响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 额外数据
     * @return Response
     */
    protected function error($message = 'error', $code = 400, $data = null)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];

        return json($response);
    }

    /**
     * 分页响应
     * @param array $list 数据列表
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return Response
     */
    protected function paginate($list, $total, $page, $limit, $message = 'success')
    {
        $data = [
            'list' => $list,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];

        return $this->success($data, $message);
    }

    /**
     * 参数验证失败响应
     * @param string $message 验证失败消息
     * @return Response
     */
    protected function validateError($message)
    {
        return $this->error($message, 422);
    }

    /**
     * 未授权响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function unauthorized($message = '未授权访问')
    {
        return $this->error($message, 401);
    }

    /**
     * 禁止访问响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function forbidden($message = '禁止访问')
    {
        return $this->error($message, 403);
    }

    /**
     * 资源不存在响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function notFound($message = '资源不存在')
    {
        return $this->error($message, 404);
    }

    /**
     * 服务器错误响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function serverError($message = '服务器内部错误')
    {
        return $this->error($message, 500);
    }

    /**
     * 处理异常并返回响应
     * @param \Exception $e 异常对象
     * @return Response
     */
    protected function handleException(\Exception $e)
    {
        // 记录异常日志
        $this->logAction('exception', [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);

        // 根据异常类型返回不同响应
        if ($e instanceof \InvalidArgumentException) {
            return $this->validateError($e->getMessage());
        }

        // 生产环境不暴露具体错误信息
        if (app()->isDebug()) {
            return $this->serverError($e->getMessage());
        } else {
            return $this->serverError('服务器内部错误');
        }
    }

    /**
     * 统一异常处理
     * @param callable $callback 业务逻辑回调
     * @return Response
     */
    protected function handleRequest(callable $callback)
    {
        try {
            return $callback();
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
