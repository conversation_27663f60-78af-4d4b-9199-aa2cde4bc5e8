<?php
namespace app\controller\common;

use think\App;
use think\Request;
use think\Response;

/**
 * 基础控制器类
 * 提供公共方法和统一的响应格式
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var Request
     */
    protected $request;

    /**
     * 应用实例
     * @var App
     */
    protected $app;

    /**
     * 构造方法
     * @param App $app
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化方法
     */
    protected function initialize()
    {
        // 子类可以重写此方法进行初始化
    }

    /**
     * 获取请求参数
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getParam($name, $default = null)
    {
        return $this->request->param($name, $default);
    }

    /**
     * 获取POST参数
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getPost($name, $default = null)
    {
        return $this->request->post($name, $default);
    }

    /**
     * 获取GET参数
     * @param string $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getGet($name, $default = null)
    {
        return $this->request->get($name, $default);
    }

    /**
     * 获取请求头中的Token
     * @return string|null
     */
    protected function getToken()
    {
        return $this->request->header('token');
    }

    /**
     * 获取用户IP
     * @return string
     */
    protected function getUserIp()
    {
        return $this->request->ip();
    }

    /**
     * 获取User-Agent
     * @return string
     */
    protected function getUserAgent()
    {
        return $this->request->header('user-agent', '');
    }

    /**
     * 验证必需参数
     * @param array $params 参数数组
     * @param array $required 必需参数列表
     * @throws \Exception
     */
    protected function validateRequired($params, $required)
    {
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '') {
                throw new \Exception("参数 {$field} 不能为空");
            }
        }
    }

    /**
     * 记录操作日志
     * @param string $action 操作名称
     * @param array $data 操作数据
     */
    protected function logAction($action, $data = [])
    {
        $logData = [
            'action' => $action,
            'ip' => $this->getUserIp(),
            'user_agent' => $this->getUserAgent(),
            'data' => $data,
            'time' => date('Y-m-d H:i:s')
        ];
        
        // 这里可以调用日志服务记录
        // LogService::record($logData);
    }
}
