<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use think\facade\Log;
use app\util\PermissionHelper;
use app\service\UidService;

class Auth extends BaseController
{
    /**
     * 提交认证申请
     */
    public function submitApplication(Request $request)
    {
        // 验证token
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        // 验证JWT token
        try {
            $payload = \app\util\JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
            $userId = $payload['user_id'];
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token验证失败']);
        }

        $schoolId = $request->param('school_id');
        $schoolName = $request->param('school_name');
        $imageUrl = $request->param('image_url');

        if (!$schoolId || !$schoolName || !$imageUrl) {
            return json(['code' => 400, 'msg' => '参数不完整']);
        }

        try {
            // 获取用户信息
            $user = Db::table('user')->where('id', $userId)->find();
            if (!$user) {
                return json(['code' => 400, 'msg' => '用户不存在']);
            }

            // 如果用户已通过认证，直接提示无需上传
            if (PermissionHelper::hasBasePermission($userId)) {
                return json(['code' => 200, 'msg' => '无需上传，您已通过认证']);
            }

            // 检查是否已有待审核的申请
            $existingApplication = Db::table('auth_applications')
                ->where('user_id', $userId)
                ->where('status', 'pending')
                ->find();

            if ($existingApplication) {
                return json(['code' => 400, 'msg' => '您已有待审核的认证申请，请耐心等待']);
            }

            // 若用户已通过认证，则直接提示无需上传
            if (PermissionHelper::hasBasePermission($userId)) {
                return json([
                    'code' => 200,
                    'msg'  => '无需上传，您已通过认证'
                ]);
            }

            // 创建认证申请记录
            $applicationId = Db::table('auth_applications')->insertGetId([
                'user_id' => $userId,
                'username' => $user['username'],
                'school_id' => $schoolId,
                'school_name' => $schoolName,
                'image_url' => $imageUrl,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 发送通知给管理员
            $notificationController = new \app\controller\Notification($this->app);
            $notificationController->sendToAdmins(
                'student_auth',
                '新的学生认证申请',
                "用户 {$user['username']} 提交了 {$schoolName} 的学生认证申请，请及时审核。",
                $applicationId,
                '/images/xuesheng.png'
            );

            return json([
                'code' => 200,
                'msg' => '认证申请提交成功，请等待管理员审核',
                'data' => [
                    'application_id' => $applicationId
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '提交失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取认证申请列表（管理员用）
     */
    public function getApplications(Request $request)
    {
        // 验证token
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        // 验证JWT token和管理员权限
        try {
            $payload = \app\util\JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 检查是否为管理员
            if (!PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token验证失败']);
        }

        $page = $request->param('page', 1);
        $pageSize = $request->param('page_size', 20);
        $status = $request->param('status', 'pending');

        try {
            $applications = Db::table('auth_applications')
                ->where('status', $status)
                ->order('created_at', 'desc')
                ->page($page, $pageSize)
                ->select();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $applications
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 审核认证申请
     */
    public function reviewApplication(Request $request)
    {
        // 验证token
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        // 验证JWT token和管理员权限
        try {
            $payload = \app\util\JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 检查是否为管理员
            if (!PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token验证失败']);
        }

        $applicationId = $request->param('application_id');
        $action = $request->param('action'); // 'approve' 或 'reject'
        $reason = $request->param('reason', '');

        if (!$applicationId || !in_array($action, ['approve', 'reject'])) {
            return json(['code' => 400, 'msg' => '参数错误']);
        }

        try {
            // 获取申请信息
            $application = Db::table('auth_applications')
                ->where('id', $applicationId)
                ->find();

            // 审核幂等与宽松策略：
            // 1) 如果申请已被处理，但对应用户已是verified，则返回提示并将本申请状态对齐为approved（如有需要）
            if ($application && $application['status'] !== 'pending') {
                // 检查用户是否已通过认证
                if (PermissionHelper::hasBasePermission($application['user_id']) && $application['status'] !== 'approved') {
                    Db::table('auth_applications')->where('id', $applicationId)->update([
                        'status' => 'approved',
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
                return json(['code' => 200, 'msg' => '该用户已完成认证，无需重复审核']);
            }


            if (!$application) {
                return json(['code' => 400, 'msg' => '该申请已被其他管理员审核过']);
            }

            Db::startTrans();

            if ($action === 'approve') {
                // 批准申请
                Db::table('auth_applications')
                    ->where('id', $applicationId)
                    ->update([
                        'status' => 'approved',
                        'reviewed_by' => $payload['user_id'],
                        'reviewed_at' => date('Y-m-d H:i:s'),
                        'reason' => $reason,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                // 更新用户认证状态（按你的要求：status=verified，status_code+1，verified_university_id=school_id）
                Db::table('user')
                    ->where('id', $application['user_id'])
                    ->update([
                        'status' => 'verified',
                        'status_code' => Db::raw('status_code + 1'),
                        'verified_university_id' => $application['school_id']
                    ]);

                // 为用户分配学校内的UID
                $uid = UidService::assignUidToUser($application['user_id'], $application['school_id']);
                if ($uid) {
                    Log::info("用户认证通过，已分配UID", [
                        'user_id' => $application['user_id'],
                        'school_id' => $application['school_id'],
                        'uid' => $uid
                    ]);
                } else {
                    Log::error("用户认证通过但UID分配失败", [
                        'user_id' => $application['user_id'],
                        'school_id' => $application['school_id']
                    ]);
                }

                $message = '您的学生认证申请已通过审核' . ($reason ? "。审核意见：{$reason}" : '') . '。';
            } else {
                // 拒绝申请
                Db::table('auth_applications')
                    ->where('id', $applicationId)
                    ->update([
                        'status' => 'rejected',
                        'reviewed_by' => $payload['user_id'],
                        'reviewed_at' => date('Y-m-d H:i:s'),
                        'reason' => $reason,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                $message = '您的学生认证申请未通过审核' . ($reason ? "。审核意见：{$reason}" : '') . '。';
            }

            // 发送通知给申请用户
            Db::name('notification')->insert([
                'user_id' => $application['user_id'],
                'from_user_id' => 0, // 系统通知
                'type' => 'auth_result',
                'target_type' => 'auth_result',
                'target_id' => $applicationId,
                'message_id' => 0,
                'content' => $action === 'approve' ? '认证审核通过' : '认证审核拒绝',
                'target_content' => $message,
                'content_image' => $action === 'approve' ? '/images/chenggong.png' : '/images/shibai.png',
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ]);



            // 审核完成后，将该申请对应的管理员通知置为已读（除当前审核人外）
            try {
                Db::name('notification')
                    ->where('type', 'student_auth')
                    ->where('target_id', $applicationId)
                    ->where('user_id', '<>', $payload['user_id'])
                    ->where('is_read', 0)
                    ->update(['is_read' => 1]);
            } catch (\Exception $ignore) {
                // 标记已读失败不影响审核主流程
            }

            Db::commit();

            return json([
                'code' => 200,
                'msg' => $action === 'approve' ? '审核通过' : '审核拒绝'
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'msg' => '审核失败：' . $e->getMessage()]);
        }
    }



    /**
     * 获取单个认证申请详情（管理员用）
     * GET /auth/getApplicationDetail?id={application_id}
     */
    public function getApplicationDetail(Request $request)
    {
        // 验证token
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        // 验证JWT token和管理员权限
        try {
            $payload = \app\util\JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 检查是否为管理员
            if (!PermissionHelper::hasAdminPermission($payload['user_id'])) {
                return json(['code' => 403, 'msg' => '权限不足']);
            }
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token验证失败']);
        }

        $applicationId = (int)$request->param('id', 0);
        if ($applicationId <= 0) {
            return json(['code' => 400, 'msg' => '缺少或非法的申请ID']);
        }

        try {
            $application = Db::table('auth_applications')->where('id', $applicationId)->find();
            if (!$application) {
                return json(['code' => 404, 'msg' => '申请不存在']);
            }

            return json([
                'code' => 200,
                'msg'  => '获取成功',
                'data' => $application
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

}


