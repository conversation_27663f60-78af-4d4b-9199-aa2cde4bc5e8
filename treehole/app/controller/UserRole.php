<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\service\PermissionService;
use app\validate\UserRoleValidate;
use think\Request;
use think\response\Json;
use think\facade\Db;
use think\facade\Log;

/**
 * 用户角色管理控制器
 */
class UserRole extends BaseController
{
    protected $permissionService;

    public function __construct()
    {
        $this->permissionService = new PermissionService();
    }

    /**
     * 获取用户角色列表
     * @param Request $request
     * @return Json
     */
    public function getUserRoles(Request $request): Json
    {
        try {
            // Token验证 - 采用与其他控制器相同的方式
            $token = $request->header('token');
            if (!$token) {
                return json(['error_code' => 1, 'msg' => '请先登录']);
            }

            $userData = \app\util\JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['error_code' => 1, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];
            $targetUserId = $request->param('target_user_id');
            if (!$targetUserId) {
                $targetUserId = $userId; // 默认查询自己的角色
            }

            // 检查权限：只能查看自己的角色，或者管理员可以查看所有人的角色
            if ($targetUserId != $userId && !$this->permissionService->hasBasePermission($userId, '管理员')) {
                return json([
                    'error_code' => 2,
                    'msg' => '没有权限查看其他用户的角色'
                ]);
            }

            $roles = $this->permissionService->getUserRoles((int)$targetUserId);

            return json([
                'error_code' => 0,
                'msg' => '获取成功',
                'data' => $roles
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户角色失败: ' . $e->getMessage());
            return json([
                'error_code' => 3,
                'msg' => '获取失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 授予用户角色
     * @param Request $request
     * @return Json
     */
    public function grantRole(Request $request): Json
    {
        try {
            // Token验证
            $token = $request->header('token');
            if (!$token) {
                return json(['error_code' => 1, 'msg' => '请先登录']);
            }

            $userData = \app\util\JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['error_code' => 1, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];

            // 验证参数
            $validate = new UserRoleValidate();
            if (!$validate->scene('grant')->check($request->param())) {
                return json([
                    'error_code' => 2,
                    'msg' => $validate->getError()
                ]);
            }

            $targetUserId = $request->param('target_user_id');
            $roleType = $request->param('role_type');
            $targetId = $request->param('target_id');
            $targetType = $request->param('target_type');
            $expiresAt = $request->param('expires_at');

            // 检查授权权限
            if (!$this->canGrantRole($userId, $roleType, $targetId, $targetType)) {
                return json([
                    'error_code' => 3,
                    'msg' => '没有权限授予此角色'
                ]);
            }

            // 检查目标用户是否存在
            $targetUser = Db::name('user')->where('id', $targetUserId)->find();
            if (!$targetUser) {
                return json([
                    'error_code' => 3,
                    'msg' => '目标用户不存在'
                ]);
            }

            // 检查目标资源是否存在
            if (!$this->checkTargetExists($targetId, $targetType)) {
                return json([
                    'error_code' => 4,
                    'msg' => '目标资源不存在'
                ]);
            }

            // 授予角色
            $result = $this->permissionService->grantRole(
                $targetUserId,
                $roleType,
                $targetId,
                $targetType,
                $userId,
                $expiresAt
            );

            if ($result) {
                Log::info("角色授予成功 - 授权人: {$userId}, 目标用户: {$targetUserId}, 角色: {$roleType}");
                return json([
                    'error_code' => 0,
                    'msg' => '授予成功'
                ]);
            } else {
                return json([
                    'error_code' => 5,
                    'msg' => '授予失败，可能角色已存在'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('授予角色失败: ' . $e->getMessage());
            return json([
                'error_code' => 6,
                'msg' => '授予失败'
            ]);
        }
    }

    /**
     * 撤销用户角色
     * @param Request $request
     * @return Json
     */
    public function revokeRole(Request $request): Json
    {
        try {
            // Token验证
            $token = $request->header('token');
            if (!$token) {
                return json(['error_code' => 1, 'msg' => '请先登录']);
            }

            $userData = \app\util\JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['error_code' => 1, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];

            // 验证参数
            $validate = new UserRoleValidate();
            if (!$validate->scene('revoke')->check($request->param())) {
                return json([
                    'error_code' => 2,
                    'msg' => $validate->getError()
                ]);
            }

            $targetUserId = $request->param('target_user_id');
            $roleType = $request->param('role_type');
            $targetId = $request->param('target_id');
            $targetType = $request->param('target_type');

            // 检查撤销权限
            if (!$this->canRevokeRole($userId, $roleType, $targetId, $targetType)) {
                return json([
                    'error_code' => 2,
                    'msg' => '没有权限撤销此角色'
                ]);
            }

            // 撤销角色
            $result = $this->permissionService->revokeRole(
                $targetUserId,
                $roleType,
                $targetId,
                $targetType
            );

            if ($result) {
                Log::info("角色撤销成功 - 操作人: {$userId}, 目标用户: {$targetUserId}, 角色: {$roleType}");
                return json([
                    'error_code' => 0,
                    'msg' => '撤销成功'
                ]);
            } else {
                return json([
                    'error_code' => 3,
                    'msg' => '撤销失败'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('撤销角色失败: ' . $e->getMessage());
            return json([
                'error_code' => 4,
                'msg' => '撤销失败'
            ]);
        }
    }

    /**
     * 获取角色类型列表
     * @param Request $request
     * @return Json
     */
    public function getRoleTypes(Request $request): Json
    {
        return json([
            'error_code' => 0,
            'msg' => '获取成功',
            'data' => [
                'role_types' => PermissionService::ROLE_TYPES,
                'target_types' => PermissionService::TARGET_TYPES
            ]
        ]);
    }



    /**
     * 批量检查权限
     * @param Request $request
     * @return Json
     */
    public function checkPermissions(Request $request): Json
    {
        try {
            // Token验证
            $token = $request->header('token');
            if (!$token) {
                return json(['error_code' => 1, 'msg' => '请先登录']);
            }

            $userData = \app\util\JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['error_code' => 1, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];
            $permissions = $request->param('permissions', []);

            // 如果permissions是字符串，尝试解析JSON
            if (is_string($permissions)) {
                $permissions = json_decode($permissions, true);
            }

            if (empty($permissions) || !is_array($permissions)) {
                return json([
                    'error_code' => 2,
                    'msg' => '权限列表不能为空'
                ]);
            }

            $results = $this->permissionService->checkMultiplePermissions($userId, $permissions);

            return json([
                'error_code' => 0,
                'msg' => '检查完成',
                'data' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('批量权限检查失败: ' . $e->getMessage());
            return json([
                'error_code' => 2,
                'msg' => '检查失败'
            ]);
        }
    }

    /**
     * 检查是否可以授予角色
     * @param int $userId 操作用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @return bool
     */
    private function canGrantRole(int $userId, string $roleType, int $targetId, string $targetType): bool
    {
        // 超级管理员可以授予所有角色
        if ($this->permissionService->hasBasePermission($userId, '超级管理员')) {
            return true;
        }

        // 管理员可以授予本校相关的角色
        if ($this->permissionService->hasBasePermission($userId, '管理员')) {
            // 这里可以添加学校范围的检查
            return true;
        }

        // 群主可以授予群管理员角色
        if ($roleType === 'group_admin' && $targetType === 'group') {
            return $this->permissionService->hasRole($userId, 'group_owner', $targetId, 'group');
        }

        // 组织管理员可以授予组织相关角色
        if (in_array($roleType, ['club_admin']) && $targetType === 'organization') {
            return $this->permissionService->hasRole($userId, 'club_admin', $targetId, 'organization') ||
                   $this->permissionService->canManageOrganization($userId, $targetId);
        }

        return false;
    }

    /**
     * 检查是否可以撤销角色
     * @param int $userId 操作用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @return bool
     */
    private function canRevokeRole(int $userId, string $roleType, int $targetId, string $targetType): bool
    {
        // 使用与授予相同的权限检查逻辑
        return $this->canGrantRole($userId, $roleType, $targetId, $targetType);
    }

    /**
     * 检查目标资源是否存在
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @return bool
     */
    private function checkTargetExists(int $targetId, string $targetType): bool
    {
        switch ($targetType) {
            case 'group':
                return Db::name('qun')->where('id', $targetId)->count() > 0;
            case 'organization':
                return Db::name('organizations')->where('id', $targetId)->count() > 0;
            case 'official_account':
                // 如果有公众号表，在这里检查
                return true;
            case 'business':
                // 如果有商家表，在这里检查
                return true;
            case 'institution':
                // 如果有机构表，在这里检查
                return true;
            default:
                return false;
        }
    }
}
