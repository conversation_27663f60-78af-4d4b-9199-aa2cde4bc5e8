<?php
namespace app\service\system;

use think\facade\Db;
use think\facade\Log;
use app\service\CacheService;
use app\service\DatabaseAdapter;
use app\service\AlertService;

class WeeklyReportService
{
    /**
     * 生成并发送周报
     */
    public function generateAndSendWeeklyReport()
    {
        try {
            $reportData = $this->collectWeeklyData();
            $reportContent = $this->formatReportContent($reportData);
            $reportFile = $this->saveReportToFile($reportData);
            
            // 发送邮件
            $this->sendWeeklyReportEmail($reportContent, $reportFile);
            
            Log::info('周报生成并发送成功', ['report_file' => $reportFile]);
            return true;
            
        } catch (\Exception $e) {
            Log::error('周报生成失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 收集一周的数据
     */
    private function collectWeeklyData()
    {
        $endTime = time();
        $startTime = $endTime - 7 * 24 * 3600; // 7天前
        
        $data = [
            'report_period' => [
                'start_date' => date('Y-m-d', $startTime),
                'end_date' => date('Y-m-d', $endTime),
                'generated_at' => date('Y-m-d H:i:s')
            ],
            'security_summary' => $this->getSecuritySummary($startTime, $endTime),
            'user_activity' => $this->getUserActivity($startTime, $endTime),
            'system_performance' => $this->getSystemPerformance(),
            'cache_statistics' => $this->getCacheStatistics(),
            'database_status' => $this->getDatabaseStatus(),
            'threat_analysis' => $this->getThreatAnalysis($startTime, $endTime),
            'recommendations' => $this->getRecommendations()
        ];
        
        return $data;
    }
    
    /**
     * 获取安全摘要
     */
    private function getSecuritySummary($startTime, $endTime)
    {
        // 统计安全扫描结果
        $scanFiles = glob(app()->getRootPath() . 'logs/security_scan_*.json');
        $weekScanFiles = array_filter($scanFiles, function($file) use ($startTime, $endTime) {
            $fileTime = filemtime($file);
            return $fileTime >= $startTime && $fileTime <= $endTime;
        });
        
        $totalMaliciousUsers = 0;
        $totalSuspiciousPosts = 0;
        $totalAbnormalRequests = 0;
        $scanCount = 0;
        
        foreach ($weekScanFiles as $file) {
            $scanData = json_decode(file_get_contents($file), true);
            if ($scanData) {
                $totalMaliciousUsers += $scanData['malicious_users_count'] ?? 0;
                $totalSuspiciousPosts += $scanData['suspicious_posts_count'] ?? 0;
                $totalAbnormalRequests += $scanData['abnormal_requests_count'] ?? 0;
                $scanCount++;
            }
        }
        
        return [
            'scan_count' => $scanCount,
            'malicious_users_found' => $totalMaliciousUsers,
            'suspicious_posts_found' => $totalSuspiciousPosts,
            'abnormal_requests_detected' => $totalAbnormalRequests,
            'security_level' => $this->calculateSecurityLevel($totalMaliciousUsers, $totalSuspiciousPosts, $totalAbnormalRequests)
        ];
    }
    
    /**
     * 获取用户活动统计
     */
    private function getUserActivity($startTime, $endTime)
    {
        $stats = [
            'total_users' => 0,
            'new_users' => 0,
            'active_users' => 0,
            'total_messages' => 0,
            'new_messages' => 0
        ];
        
        try {
            // 总用户数
            if (DatabaseAdapter::tableExists('user')) {
                $stats['total_users'] = Db::name('user')->count();
            }
            
            // 消息统计
            if (DatabaseAdapter::tableExists('message')) {
                $stats['total_messages'] = Db::name('message')->count();
                
                // 本周新消息
                $timeCondition = DatabaseAdapter::buildTimeRangeCondition('message', 7);
                if (!empty($timeCondition)) {
                    $stats['new_messages'] = Db::name('message')
                        ->where($timeCondition[0], $timeCondition[1], $timeCondition[2])
                        ->count();
                }
            }
            
        } catch (\Exception $e) {
            Log::warning('获取用户活动统计失败', ['error' => $e->getMessage()]);
        }
        
        return $stats;
    }
    
    /**
     * 获取系统性能数据
     */
    private function getSystemPerformance()
    {
        $performance = [
            'php_version' => PHP_VERSION,
            'memory_usage' => [
                'current' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB'
            ],
            'disk_usage' => $this->getDiskUsage(),
            'log_file_sizes' => $this->getLogFileSizes()
        ];
        
        return $performance;
    }
    
    /**
     * 获取缓存统计
     */
    private function getCacheStatistics()
    {
        $stats = CacheService::getCacheStats();
        
        // 添加一周内的Redis故障统计
        $redisFailures = $this->getRedisFailureStats();
        $stats['weekly_failures'] = $redisFailures;
        
        return $stats;
    }
    
    /**
     * 获取数据库状态
     */
    private function getDatabaseStatus()
    {
        return DatabaseAdapter::generateStructureReport();
    }
    
    /**
     * 获取威胁分析
     */
    private function getThreatAnalysis($startTime, $endTime)
    {
        // 分析日志中的威胁模式
        $logFile = app()->getRootPath() . 'logs/php_error.log';
        $threats = [
            'sql_injection_attempts' => 0,
            'high_frequency_requests' => 0,
            'suspicious_user_agents' => 0,
            'blocked_ips' => []
        ];
        
        if (file_exists($logFile)) {
            $logs = file($logFile);
            $recentLogs = array_slice($logs, -10000); // 最近10000行
            
            foreach ($recentLogs as $log) {
                if (strpos($log, 'SQL注入攻击尝试') !== false) {
                    $threats['sql_injection_attempts']++;
                }
                if (strpos($log, '高频请求告警') !== false) {
                    $threats['high_frequency_requests']++;
                }
                if (strpos($log, '可疑User-Agent') !== false) {
                    $threats['suspicious_user_agents']++;
                }
            }
        }
        
        return $threats;
    }
    
    /**
     * 获取建议
     */
    private function getRecommendations()
    {
        $recommendations = [];
        
        // 基于缓存状态的建议
        $cacheStats = CacheService::getCacheStats();
        if (!$cacheStats['redis_available']) {
            $recommendations[] = '🔧 Redis服务当前不可用，建议检查并修复Redis服务以提升系统性能';
        }
        
        if ($cacheStats['file_cache_count'] > 100) {
            $recommendations[] = '🧹 文件缓存数量较多(' . $cacheStats['file_cache_count'] . '个)，建议清理过期缓存';
        }
        
        // 基于磁盘使用的建议
        $diskUsage = $this->getDiskUsage();
        if ($diskUsage > 80) {
            $recommendations[] = "💾 磁盘使用率较高({$diskUsage}%)，建议清理日志文件或扩容";
        }
        
        // 基于日志大小的建议
        $logSizes = $this->getLogFileSizes();
        foreach ($logSizes as $file => $size) {
            if ($size > 100) { // 超过100MB
                $recommendations[] = "📝 日志文件 {$file} 较大({$size}MB)，建议进行日志轮转";
            }
        }
        
        if (empty($recommendations)) {
            $recommendations[] = '✅ 系统运行良好，暂无特别建议';
        }
        
        return $recommendations;
    }
    
    /**
     * 格式化报告内容
     */
    private function formatReportContent($data)
    {
        $content = "# 🛡️ 树洞系统安全监控周报\n\n";
        $content .= "**报告周期：** {$data['report_period']['start_date']} 至 {$data['report_period']['end_date']}\n";
        $content .= "**生成时间：** {$data['report_period']['generated_at']}\n\n";
        
        // 安全摘要
        $security = $data['security_summary'];
        $content .= "## 📊 安全摘要\n\n";
        $content .= "- **安全扫描次数：** {$security['scan_count']} 次\n";
        $content .= "- **发现恶意用户名：** {$security['malicious_users_found']} 个\n";
        $content .= "- **可疑帖子：** {$security['suspicious_posts_found']} 个\n";
        $content .= "- **异常请求：** {$security['abnormal_requests_detected']} 次\n";
        $content .= "- **安全等级：** {$security['security_level']}\n\n";
        
        // 用户活动
        $activity = $data['user_activity'];
        $content .= "## 👥 用户活动统计\n\n";
        $content .= "- **总用户数：** {$activity['total_users']} 人\n";
        $content .= "- **总消息数：** {$activity['total_messages']} 条\n";
        $content .= "- **本周新消息：** {$activity['new_messages']} 条\n\n";
        
        // 系统性能
        $performance = $data['system_performance'];
        $content .= "## ⚡ 系统性能\n\n";
        $content .= "- **PHP版本：** {$performance['php_version']}\n";
        $content .= "- **内存使用：** {$performance['memory_usage']['current']} (峰值: {$performance['memory_usage']['peak']})\n";
        $content .= "- **磁盘使用率：** {$performance['disk_usage']}%\n\n";
        
        // 缓存统计
        $cache = $data['cache_statistics'];
        $content .= "## 💾 缓存系统\n\n";
        $content .= "- **Redis状态：** " . ($cache['redis_available'] ? '✅ 正常' : '❌ 不可用') . "\n";
        $content .= "- **当前缓存类型：** {$cache['cache_type']}\n";
        $content .= "- **文件缓存数量：** {$cache['file_cache_count']} 个\n\n";
        
        // 威胁分析
        $threats = $data['threat_analysis'];
        $content .= "## 🚨 威胁分析\n\n";
        $content .= "- **SQL注入尝试：** {$threats['sql_injection_attempts']} 次\n";
        $content .= "- **高频请求告警：** {$threats['high_frequency_requests']} 次\n";
        $content .= "- **可疑User-Agent：** {$threats['suspicious_user_agents']} 次\n\n";
        
        // 建议
        $content .= "## 💡 建议和改进\n\n";
        foreach ($data['recommendations'] as $recommendation) {
            $content .= "- {$recommendation}\n";
        }
        
        $content .= "\n---\n";
        $content .= "*此报告由树洞安全监控系统自动生成*\n";
        $content .= "*如有问题，请联系系统管理员*\n";
        
        return $content;
    }
    
    /**
     * 保存报告到文件
     */
    private function saveReportToFile($data)
    {
        $filename = 'weekly_report_' . date('Y-m-d') . '.json';
        $filepath = app()->getRootPath() . 'logs/' . $filename;
        
        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        return $filepath;
    }
    
    /**
     * 发送周报邮件
     */
    private function sendWeeklyReportEmail($content, $reportFile)
    {
        $alertService = new AlertService();
        
        $subject = '🛡️ 树洞系统安全监控周报 - ' . date('Y年m月d日');
        
        // 邮件内容转换为纯文本格式
        $emailContent = str_replace(['#', '*'], '', $content);
        $emailContent .= "\n\n详细数据请查看附件：" . basename($reportFile);
        
        return $alertService->sendEmail(
            config('secrets.security.admin_email'),
            $subject,
            $emailContent
        );
    }
    
    // 辅助方法
    private function calculateSecurityLevel($malicious, $suspicious, $abnormal)
    {
        $total = $malicious + $suspicious + $abnormal;
        
        if ($total == 0) return '🟢 安全';
        if ($total <= 5) return '🟡 注意';
        if ($total <= 20) return '🟠 警告';
        return '🔴 高危';
    }
    
    private function getDiskUsage()
    {
        $bytes = disk_free_space('.');
        $total = disk_total_space('.');
        return round((($total - $bytes) / $total) * 100, 1);
    }
    
    private function getLogFileSizes()
    {
        $logDir = app()->getRootPath() . 'logs/';
        $sizes = [];
        
        if (is_dir($logDir)) {
            $files = glob($logDir . '*.log');
            foreach ($files as $file) {
                $size = round(filesize($file) / 1024 / 1024, 2);
                $sizes[basename($file)] = $size;
            }
        }
        
        return $sizes;
    }
    
    private function getRedisFailureStats()
    {
        // 这里可以从日志中统计Redis故障次数
        return [
            'failure_count' => CacheService::getCacheStats()['redis_failure_count'],
            'current_status' => CacheService::isRedisAvailable() ? '正常' : '故障'
        ];
    }
}
