<?php
namespace app\service\system;

use think\facade\Log;
use think\facade\Cache;

// 引入 PHPMailer
require_once public_path() . 'PHPMailer/src/PHPMailer.php';
require_once public_path() . 'PHPMailer/src/SMTP.php';
require_once public_path() . 'PHPMailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class AlertService
{
    private $adminEmail;
    private $alertThreshold;
    
    public function __construct()
    {
        // 从配置文件读取管理员邮箱
        $this->adminEmail = config('secrets.security.admin_email', '');
        $this->alertThreshold = [
            'malicious_users' => 1,      // 发现1个恶意用户就告警
            'suspicious_posts' => 5,     // 发现5个可疑帖子告警
            'abnormal_requests' => 10,   // 发现10个异常请求告警
            'failed_logins' => 20        // 发现20个失败登录告警
        ];
    }
    
    /**
     * 发送安全告警邮件
     */
    public function sendSecurityAlert($scanResults)
    {
        if (empty($this->adminEmail)) {
            Log::warning('未配置管理员邮箱，无法发送告警邮件');
            return false;
        }
        
        $alertLevel = $this->calculateAlertLevel($scanResults);
        
        if ($alertLevel === 'none') {
            return true; // 无需告警
        }
        
        $subject = $this->getAlertSubject($alertLevel);
        $content = $this->generateAlertContent($scanResults, $alertLevel);
        
        return $this->sendEmail($this->adminEmail, $subject, $content);
    }
    
    /**
     * 计算告警级别
     */
    private function calculateAlertLevel($results)
    {
        $maliciousCount = count($results['malicious_users']);
        $suspiciousCount = count($results['suspicious_posts']);
        $abnormalCount = count($results['abnormal_requests']);
        $failedCount = count($results['failed_logins']);
        
        // 高危告警
        if ($maliciousCount >= 5 || $abnormalCount >= 50) {
            return 'critical';
        }
        
        // 中危告警
        if ($maliciousCount >= $this->alertThreshold['malicious_users'] ||
            $suspiciousCount >= $this->alertThreshold['suspicious_posts'] ||
            $abnormalCount >= $this->alertThreshold['abnormal_requests'] ||
            $failedCount >= $this->alertThreshold['failed_logins']) {
            return 'warning';
        }
        
        return 'none';
    }
    
    /**
     * 获取告警主题
     */
    private function getAlertSubject($level)
    {
        $subjects = [
            'critical' => '🚨 【紧急】树洞系统安全告警 - 发现严重威胁',
            'warning' => '⚠️ 【警告】树洞系统安全告警 - 发现安全风险'
        ];
        
        return $subjects[$level] ?? '安全告警';
    }
    
    /**
     * 生成告警内容
     */
    private function generateAlertContent($results, $level)
    {
        $content = "树洞系统安全扫描报告\n";
        $content .= "扫描时间: " . date('Y-m-d H:i:s') . "\n";
        $content .= "告警级别: " . strtoupper($level) . "\n\n";
        
        $content .= "=== 扫描结果统计 ===\n";
        $content .= "恶意用户名: " . count($results['malicious_users']) . " 个\n";
        $content .= "可疑帖子: " . count($results['suspicious_posts']) . " 个\n";
        $content .= "异常请求: " . count($results['abnormal_requests']) . " 个\n";
        $content .= "失败登录: " . count($results['failed_logins']) . " 个\n\n";
        
        // 详细信息
        if (!empty($results['malicious_users'])) {
            $content .= "=== 恶意用户名详情 ===\n";
            foreach (array_slice($results['malicious_users'], 0, 10) as $user) {
                $content .= "用户ID: {$user['id']}, 用户名: {$user['username']}, 原因: {$user['reason']}\n";
            }
            $content .= "\n";
        }
        
        if (!empty($results['abnormal_requests'])) {
            $content .= "=== 异常请求详情 ===\n";
            foreach (array_slice($results['abnormal_requests'], 0, 10) as $request) {
                $content .= "类型: {$request['type']}, 时间: {$request['time']}\n";
            }
            $content .= "\n";
        }
        
        $content .= "=== 建议处理措施 ===\n";
        $content .= "1. 立即登录系统检查详细日志\n";
        $content .= "2. 运行清理脚本清除恶意数据\n";
        $content .= "3. 检查防火墙和安全策略\n";
        $content .= "4. 考虑临时加强访问限制\n\n";
        
        $content .= "详细日志请查看: logs/security_scan_" . date('Y-m-d') . ".json\n";
        
        return $content;
    }
    
    /**
     * 发送邮件 - 使用PHPMailer
     */
    public function sendEmail($to, $subject, $content)
    {
        try {
            $mail = new PHPMailer(true);

            // SMTP配置
            $mail->isSMTP();
            $mail->Host = 'smtp.qq.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';  // 发件邮箱
            $mail->Password = 'auwcvrhmwqubdbhi';   // 授权码
            $mail->SMTPSecure = 'ssl';
            $mail->Port = 465;
            $mail->CharSet = 'UTF-8';

            // 发件人和收件人设置
            $fromName = config('secrets.security.email_from_name', '树洞安全监控系统');
            $mail->setFrom('<EMAIL>', $fromName);
            $mail->addAddress($to);

            // 邮件内容设置
            $mail->isHTML(false);
            $timestampedSubject = $subject . ' [' . date('Y-m-d H:i:s') . ']';
            $mail->Subject = $timestampedSubject;
            $mail->Body = $content;

            // 设置优先级
            $mail->Priority = 1; // 高优先级

            // 发送邮件
            $result = $mail->send();

            if ($result) {
                Log::info('安全告警邮件发送成功', [
                    'to' => $to,
                    'subject' => $timestampedSubject,
                    'from' => '<EMAIL>'
                ]);
            }

            return $result;

        } catch (Exception $e) {
            Log::error('发送告警邮件异常', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 发送实时告警
     */
    public function sendRealTimeAlert($type, $data)
    {
        // 防止告警轰炸，同类型告警5分钟内只发送一次
        $cacheKey = "alert_sent_{$type}";
        if (Cache::get($cacheKey)) {
            return false;
        }
        
        Cache::set($cacheKey, true, 300); // 5分钟
        
        $subject = "🚨 树洞系统实时告警 - {$type}";
        $content = "检测到安全威胁:\n\n";
        $content .= "类型: {$type}\n";
        $content .= "时间: " . date('Y-m-d H:i:s') . "\n";
        $content .= "详情: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        return $this->sendEmail($this->adminEmail, $subject, $content);
    }
}
