<?php
namespace app\service\system;

use think\facade\Db;
use think\facade\Log;

class DatabaseAdapter
{
    private static $tableStructures = [];
    private static $structureChecked = [];
    
    /**
     * 获取表结构信息
     */
    public static function getTableStructure($tableName)
    {
        if (isset(self::$tableStructures[$tableName])) {
            return self::$tableStructures[$tableName];
        }
        
        try {
            $columns = Db::query("DESCRIBE {$tableName}");
            $structure = [];
            
            foreach ($columns as $column) {
                $structure[$column['Field']] = [
                    'type' => $column['Type'],
                    'null' => $column['Null'],
                    'key' => $column['Key'],
                    'default' => $column['Default'],
                    'extra' => $column['Extra']
                ];
            }
            
            self::$tableStructures[$tableName] = $structure;
            return $structure;
            
        } catch (\Exception $e) {
            Log::error("获取表结构失败: {$tableName}", ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 检查字段是否存在
     */
    public static function hasField($tableName, $fieldName)
    {
        $structure = self::getTableStructure($tableName);
        return isset($structure[$fieldName]);
    }
    
    /**
     * 获取时间字段名（自适应create_time或send_timestamp等）
     */
    public static function getTimeField($tableName)
    {
        $structure = self::getTableStructure($tableName);
        
        // 优先级顺序
        $timeFields = ['create_time', 'created_at', 'send_timestamp', 'timestamp', 'time'];
        
        foreach ($timeFields as $field) {
            if (isset($structure[$field])) {
                return $field;
            }
        }
        
        return null;
    }
    
    /**
     * 获取用户ID字段名
     */
    public static function getUserIdField($tableName)
    {
        $structure = self::getTableStructure($tableName);
        
        $userIdFields = ['user_id', 'uid', 'userid'];
        
        foreach ($userIdFields as $field) {
            if (isset($structure[$field])) {
                return $field;
            }
        }
        
        return 'user_id'; // 默认返回user_id
    }
    
    /**
     * 获取内容字段名
     */
    public static function getContentField($tableName)
    {
        $structure = self::getTableStructure($tableName);
        
        $contentFields = ['content', 'message', 'text', 'body'];
        
        foreach ($contentFields as $field) {
            if (isset($structure[$field])) {
                return $field;
            }
        }
        
        return 'content'; // 默认返回content
    }
    
    /**
     * 构建安全的查询字段列表
     */
    public static function buildSafeFields($tableName, $requestedFields)
    {
        $structure = self::getTableStructure($tableName);
        $safeFields = [];
        
        foreach ($requestedFields as $field) {
            if (isset($structure[$field])) {
                $safeFields[] = $field;
            }
        }
        
        return $safeFields;
    }
    
    /**
     * 获取表的基本信息字段
     */
    public static function getBasicFields($tableName)
    {
        $structure = self::getTableStructure($tableName);
        $basicFields = ['id'];
        
        // 添加常见的基本字段
        $commonFields = ['username', 'openid', 'user_id', 'content', 'create_time', 'created_at', 'send_timestamp'];
        
        foreach ($commonFields as $field) {
            if (isset($structure[$field])) {
                $basicFields[] = $field;
            }
        }
        
        return array_unique($basicFields);
    }
    
    /**
     * 构建时间范围查询条件
     */
    public static function buildTimeRangeCondition($tableName, $days = 7)
    {
        $timeField = self::getTimeField($tableName);
        
        if (!$timeField) {
            return []; // 没有时间字段，返回空条件
        }
        
        $structure = self::getTableStructure($tableName);
        $fieldType = $structure[$timeField]['type'] ?? '';
        
        if (strpos($fieldType, 'int') !== false) {
            // 时间戳字段
            $timestamp = time() - $days * 24 * 3600;
            return [$timeField, '>', $timestamp];
        } else {
            // 日期时间字段
            $datetime = date('Y-m-d H:i:s', time() - $days * 24 * 3600);
            return [$timeField, '>', $datetime];
        }
    }
    
    /**
     * 检查表是否存在
     */
    public static function tableExists($tableName)
    {
        try {
            $tables = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($tables);
        } catch (\Exception $e) {
            Log::error("检查表是否存在失败: {$tableName}", ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 获取所有用户相关的表
     */
    public static function getUserRelatedTables()
    {
        $tables = ['user']; // 主用户表
        
        // 检查其他可能包含用户数据的表
        $possibleTables = ['message', 'comment', 'post', 'notification'];
        
        foreach ($possibleTables as $table) {
            if (self::tableExists($table) && self::hasField($table, 'user_id')) {
                $tables[] = $table;
            }
        }
        
        return $tables;
    }
    
    /**
     * 生成表结构报告
     */
    public static function generateStructureReport()
    {
        $report = [
            'scan_time' => date('Y-m-d H:i:s'),
            'tables' => []
        ];
        
        $userTables = self::getUserRelatedTables();
        
        foreach ($userTables as $table) {
            $structure = self::getTableStructure($table);
            $report['tables'][$table] = [
                'exists' => self::tableExists($table),
                'fields' => array_keys($structure),
                'time_field' => self::getTimeField($table),
                'user_id_field' => self::getUserIdField($table),
                'content_field' => self::getContentField($table),
                'basic_fields' => self::getBasicFields($table)
            ];
        }
        
        return $report;
    }
    
    /**
     * 检查数据库结构变更
     */
    public static function checkStructureChanges()
    {
        $cacheKey = 'db_structure_hash';
        $currentHash = self::generateStructureHash();
        $lastHash = CacheService::get($cacheKey);
        
        if ($lastHash && $lastHash !== $currentHash) {
            Log::warning('检测到数据库结构变更', [
                'last_hash' => $lastHash,
                'current_hash' => $currentHash,
                'time' => date('Y-m-d H:i:s')
            ]);
            
            // 发送结构变更告警
            try {
                $alertService = new AlertService();
                $data = [
                    'message' => '检测到数据库结构发生变更',
                    'suggestion' => '请检查安全监控系统是否需要更新',
                    'time' => date('Y-m-d H:i:s')
                ];
                $alertService->sendRealTimeAlert('数据库结构变更', $data);
            } catch (\Exception $e) {
                Log::error('发送数据库结构变更告警失败', ['error' => $e->getMessage()]);
            }
        }
        
        // 更新缓存
        CacheService::set($cacheKey, $currentHash, 86400); // 缓存24小时
        
        return $lastHash !== $currentHash;
    }
    
    /**
     * 生成数据库结构哈希值
     */
    private static function generateStructureHash()
    {
        $userTables = self::getUserRelatedTables();
        $structureData = [];
        
        foreach ($userTables as $table) {
            $structure = self::getTableStructure($table);
            $structureData[$table] = array_keys($structure);
        }
        
        return md5(json_encode($structureData));
    }
}
