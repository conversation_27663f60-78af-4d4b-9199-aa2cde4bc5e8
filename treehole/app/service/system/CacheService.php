<?php
namespace app\service\system;

use think\facade\Cache;
use think\facade\Log;

class CacheService
{
    private static $redisAvailable = null;
    private static $lastRedisCheck = 0;
    private static $redisCheckInterval = 60; // 60秒检查一次Redis状态
    private static $redisFailureCount = 0;
    private static $maxFailureCount = 5; // 连续失败5次后发送告警
    
    /**
     * 检查Redis是否可用
     */
    public static function isRedisAvailable()
    {
        $now = time();
        
        // 如果距离上次检查不到指定间隔，返回缓存结果
        if (self::$redisAvailable !== null && ($now - self::$lastRedisCheck) < self::$redisCheckInterval) {
            return self::$redisAvailable;
        }
        
        try {
            $cache = Cache::store('redis');
            $testKey = 'redis_health_check_' . $now;
            $cache->set($testKey, 'ok', 10);
            $result = $cache->get($testKey);
            $cache->delete($testKey);
            
            if ($result === 'ok') {
                // Redis恢复正常
                if (self::$redisAvailable === false) {
                    Log::info('Redis服务已恢复正常');
                    self::$redisFailureCount = 0;
                }
                self::$redisAvailable = true;
            } else {
                throw new \Exception('Redis测试失败');
            }
            
        } catch (\Exception $e) {
            self::$redisAvailable = false;
            self::$redisFailureCount++;
            
            Log::warning('Redis不可用', [
                'error' => $e->getMessage(),
                'failure_count' => self::$redisFailureCount,
                'time' => date('Y-m-d H:i:s')
            ]);
            
            // 连续失败达到阈值时发送告警
            if (self::$redisFailureCount >= self::$maxFailureCount) {
                self::sendRedisFailureAlert();
                self::$redisFailureCount = 0; // 重置计数，避免重复发送
            }
        }
        
        self::$lastRedisCheck = $now;
        return self::$redisAvailable;
    }
    
    /**
     * 智能缓存设置
     */
    public static function set($key, $value, $expire = 300)
    {
        try {
            if (self::isRedisAvailable()) {
                return Cache::store('redis')->set($key, $value, $expire);
            } else {
                return self::setFileCache($key, $value, $expire);
            }
        } catch (\Exception $e) {
            Log::warning('缓存设置失败，使用文件缓存', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return self::setFileCache($key, $value, $expire);
        }
    }
    
    /**
     * 智能缓存获取
     */
    public static function get($key, $default = null)
    {
        try {
            if (self::isRedisAvailable()) {
                return Cache::store('redis')->get($key, $default);
            } else {
                return self::getFileCache($key, $default);
            }
        } catch (\Exception $e) {
            Log::warning('缓存获取失败，使用文件缓存', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return self::getFileCache($key, $default);
        }
    }
    
    /**
     * 智能缓存删除
     */
    public static function delete($key)
    {
        try {
            if (self::isRedisAvailable()) {
                return Cache::store('redis')->delete($key);
            } else {
                return self::deleteFileCache($key);
            }
        } catch (\Exception $e) {
            Log::warning('缓存删除失败，使用文件缓存', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return self::deleteFileCache($key);
        }
    }
    
    /**
     * 文件缓存设置
     */
    private static function setFileCache($key, $value, $expire)
    {
        $cacheDir = app()->getRuntimePath() . 'security_cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        $cacheFile = $cacheDir . '/' . md5($key) . '.cache';
        $data = [
            'value' => $value,
            'expire_time' => time() + $expire,
            'created_at' => time()
        ];
        
        return file_put_contents($cacheFile, serialize($data)) !== false;
    }
    
    /**
     * 文件缓存获取
     */
    private static function getFileCache($key, $default = null)
    {
        $cacheDir = app()->getRuntimePath() . 'security_cache';
        $cacheFile = $cacheDir . '/' . md5($key) . '.cache';
        
        if (!file_exists($cacheFile)) {
            return $default;
        }
        
        $data = unserialize(file_get_contents($cacheFile));
        if (!$data || !isset($data['expire_time'])) {
            return $default;
        }
        
        // 检查是否过期
        if (time() > $data['expire_time']) {
            unlink($cacheFile);
            return $default;
        }
        
        return $data['value'];
    }
    
    /**
     * 文件缓存删除
     */
    private static function deleteFileCache($key)
    {
        $cacheDir = app()->getRuntimePath() . 'security_cache';
        $cacheFile = $cacheDir . '/' . md5($key) . '.cache';
        
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        
        return true;
    }
    
    /**
     * 清理过期的文件缓存
     */
    public static function cleanExpiredFileCache()
    {
        $cacheDir = app()->getRuntimePath() . 'security_cache';
        if (!is_dir($cacheDir)) {
            return;
        }
        
        $files = glob($cacheDir . '/*.cache');
        $cleanedCount = 0;
        
        foreach ($files as $file) {
            $data = @unserialize(file_get_contents($file));
            if (!$data || !isset($data['expire_time']) || time() > $data['expire_time']) {
                unlink($file);
                $cleanedCount++;
            }
        }
        
        if ($cleanedCount > 0) {
            Log::info("清理了 {$cleanedCount} 个过期的文件缓存");
        }
    }
    
    /**
     * 发送Redis故障告警
     */
    private static function sendRedisFailureAlert()
    {
        try {
            $alertService = new AlertService();
            $data = [
                'failure_count' => self::$redisFailureCount,
                'time' => date('Y-m-d H:i:s'),
                'message' => 'Redis服务连续失败，已自动切换到文件缓存',
                'suggestion' => '请检查Redis服务状态并及时修复'
            ];
            
            $alertService->sendRealTimeAlert('Redis服务故障', $data);
            
        } catch (\Exception $e) {
            Log::error('发送Redis故障告警失败', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public static function getCacheStats()
    {
        $stats = [
            'redis_available' => self::isRedisAvailable(),
            'redis_failure_count' => self::$redisFailureCount,
            'last_check_time' => self::$lastRedisCheck ? date('Y-m-d H:i:s', self::$lastRedisCheck) : '未检查',
            'cache_type' => self::isRedisAvailable() ? 'Redis' : '文件缓存'
        ];
        
        // 统计文件缓存数量
        $cacheDir = app()->getRuntimePath() . 'security_cache';
        if (is_dir($cacheDir)) {
            $files = glob($cacheDir . '/*.cache');
            $stats['file_cache_count'] = count($files);
        } else {
            $stats['file_cache_count'] = 0;
        }
        
        return $stats;
    }
}
