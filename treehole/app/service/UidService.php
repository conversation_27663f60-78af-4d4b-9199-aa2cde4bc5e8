<?php
declare(strict_types=1);

namespace app\service;

use app\model\SchoolUidCounter;
use app\model\User;
use think\facade\Db;
use think\facade\Log;

/**
 * UID分配服务
 */
class UidService
{
    /**
     * 为用户分配学校内的UID
     * @param int $userId 用户ID
     * @param int $universityId 学校ID
     * @return int|false 分配的UID，失败返回false
     */
    public static function assignUidToUser(int $userId, int $universityId)
    {
        try {
            // 检查用户是否已有UID
            $user = User::find($userId);
            if (!$user) {
                Log::error("用户不存在: {$userId}");
                return false;
            }
            
            if ($user->uid !== null) {
                Log::info("用户已有UID: {$userId}, UID: {$user->uid}");
                return $user->uid;
            }
            
            // 使用事务确保数据一致性
            return Db::transaction(function() use ($userId, $universityId, $user) {
                // 获取下一个UID
                $newUid = SchoolUidCounter::getNextUid($universityId);
                
                // 更新用户的UID
                $user->uid = $newUid;
                $user->save();
                
                Log::info("为用户分配UID成功: 用户ID={$userId}, 学校ID={$universityId}, UID={$newUid}");
                
                return $newUid;
            });
            
        } catch (\Exception $e) {
            Log::error("分配UID失败: " . $e->getMessage(), [
                'user_id' => $userId,
                'university_id' => $universityId,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 批量为已认证用户分配UID（用于数据迁移）
     * @return array 处理结果统计
     */
    public static function batchAssignUids(): array
    {
        $stats = [
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0
        ];
        
        try {
            // 查找所有已认证但没有UID的用户
            $users = User::where('verified_university_id', '<>', null)
                        ->where('uid', null)
                        ->select();
            
            $stats['total'] = count($users);
            
            foreach ($users as $user) {
                if (self::assignUidToUser($user->id, $user->verified_university_id)) {
                    $stats['success']++;
                } else {
                    $stats['failed']++;
                }
            }
            
            Log::info("批量分配UID完成", $stats);
            
        } catch (\Exception $e) {
            Log::error("批量分配UID失败: " . $e->getMessage());
            $stats['failed'] = $stats['total'] - $stats['success'];
        }
        
        return $stats;
    }
    
    /**
     * 获取学校的用户UID统计信息
     * @param int $universityId 学校ID
     * @return array 统计信息
     */
    public static function getSchoolUidStats(int $universityId): array
    {
        try {
            $currentUid = SchoolUidCounter::getCurrentUid($universityId);
            $userCount = User::where('verified_university_id', $universityId)
                            ->where('uid', '<>', null)
                            ->count();
            
            return [
                'university_id' => $universityId,
                'current_uid' => $currentUid,
                'user_count' => $userCount,
                'available_uids' => $currentUid - $userCount
            ];
            
        } catch (\Exception $e) {
            Log::error("获取学校UID统计失败: " . $e->getMessage());
            return [];
        }
    }
}
