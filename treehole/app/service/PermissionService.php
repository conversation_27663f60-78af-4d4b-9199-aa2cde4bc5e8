<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * 权限服务类
 * 处理用户基础身份和附加角色权限
 */
class PermissionService
{
    // 基础身份权限级别
    const BASE_PERMISSIONS = [
        'unverified' => 0,
        'verified' => 1,
        '管理员' => 2,
        '超级管理员' => 3,
        '禁言' => -1
    ];

    // 角色类型定义
    const ROLE_TYPES = [
        'group_owner' => '群主',
        'group_admin' => '群管理员',
        'official_account_admin' => '公众号管理员',
        'club_admin' => '社团管理员',
        'business_admin' => '商家管理员',
        'institution_admin' => '商业机构管理员'
    ];

    // 目标类型定义
    const TARGET_TYPES = [
        'group' => '群聊',
        'organization' => '组织',
        'official_account' => '公众号',
        'business' => '商家',
        'institution' => '商业机构'
    ];

    /**
     * 检查用户是否有基础权限
     * @param int $userId 用户ID
     * @param string $requiredLevel 需要的权限级别
     * @return bool
     */
    public function hasBasePermission(int $userId, string $requiredLevel = 'verified'): bool
    {
        $user = $this->getUserInfo($userId);
        if (!$user) {
            return false;
        }

        $userLevel = self::BASE_PERMISSIONS[$user['status']] ?? 0;
        $requiredLevelValue = self::BASE_PERMISSIONS[$requiredLevel] ?? 1;

        return $userLevel >= $requiredLevelValue;
    }

    /**
     * 检查用户是否有特定角色权限
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int|null $targetId 目标ID
     * @param string|null $targetType 目标类型
     * @return bool
     */
    public function hasRole(int $userId, string $roleType, ?int $targetId = null, ?string $targetType = null): bool
    {
        $cacheKey = "user_role_{$userId}_{$roleType}_{$targetId}_{$targetType}";
        
        return Cache::remember($cacheKey, function () use ($userId, $roleType, $targetId, $targetType) {
            $where = [
                'user_id' => $userId,
                'role_type' => $roleType,
                'status' => 1
            ];

            if ($targetId && $targetType) {
                $where['target_id'] = $targetId;
                $where['target_type'] = $targetType;
            }

            // 检查是否过期
            $query = Db::name('user_roles')->where($where);
            $query->where(function ($query) {
                $query->whereNull('expires_at')->whereOr('expires_at', '>', date('Y-m-d H:i:s'));
            });

            return $query->count() > 0;
        }, 300); // 缓存5分钟
    }

    /**
     * 获取用户所有角色
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserRoles(int $userId): array
    {
        $cacheKey = "user_roles_{$userId}";
        
        return Cache::remember($cacheKey, function () use ($userId) {
            return Db::name('user_roles')
                ->alias('ur')
                ->leftJoin('qun q', 'ur.target_id = q.id AND ur.target_type = "group"')
                ->leftJoin('organizations o', 'ur.target_id = o.id AND ur.target_type = "organization"')
                ->where('ur.user_id', $userId)
                ->where('ur.status', 1)
                ->where(function ($query) {
                    $query->whereNull('ur.expires_at')->whereOr('ur.expires_at', '>', date('Y-m-d H:i:s'));
                })
                ->field([
                    'ur.*',
                    'q.quntitle as group_name',
                    'o.name as organization_name'
                ])
                ->select()
                ->toArray();
        }, 300); // 缓存5分钟
    }

    /**
     * 授予用户角色
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @param int $grantedBy 授权人ID
     * @param string|null $expiresAt 过期时间
     * @return bool
     */
    public function grantRole(int $userId, string $roleType, int $targetId, string $targetType, int $grantedBy, ?string $expiresAt = null): bool
    {
        try {
            $data = [
                'user_id' => $userId,
                'role_type' => $roleType,
                'target_id' => $targetId,
                'target_type' => $targetType,
                'granted_by' => $grantedBy,
                'granted_at' => date('Y-m-d H:i:s'),
                'expires_at' => $expiresAt,
                'status' => 1
            ];

            Db::name('user_roles')->insert($data);
            
            // 清除相关缓存
            $this->clearUserRoleCache($userId);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 撤销用户角色
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @return bool
     */
    public function revokeRole(int $userId, string $roleType, int $targetId, string $targetType): bool
    {
        try {
            Db::name('user_roles')
                ->where([
                    'user_id' => $userId,
                    'role_type' => $roleType,
                    'target_id' => $targetId,
                    'target_type' => $targetType
                ])
                ->update(['status' => 0, 'update_time' => date('Y-m-d H:i:s')]);
            
            // 清除相关缓存
            $this->clearUserRoleCache($userId);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 检查用户是否可以管理群聊
     * @param int $userId 用户ID
     * @param int $groupId 群聊ID
     * @return bool
     */
    public function canManageGroup(int $userId, int $groupId): bool
    {
        // 超级管理员和管理员有所有权限
        if ($this->hasBasePermission($userId, '管理员')) {
            return true;
        }

        // 检查是否是群主或群管理员
        return $this->hasRole($userId, 'group_owner', $groupId, 'group') ||
               $this->hasRole($userId, 'group_admin', $groupId, 'group');
    }

    /**
     * 检查用户是否可以管理组织
     * @param int $userId 用户ID
     * @param int $organizationId 组织ID
     * @return bool
     */
    public function canManageOrganization(int $userId, int $organizationId): bool
    {
        // 超级管理员和管理员有所有权限
        if ($this->hasBasePermission($userId, '管理员')) {
            return true;
        }

        // 检查是否是社团管理员
        return $this->hasRole($userId, 'club_admin', $organizationId, 'organization');
    }

    /**
     * 获取用户信息
     * @param int $userId 用户ID
     * @return array|null
     */
    private function getUserInfo(int $userId): ?array
    {
        $cacheKey = "user_info_{$userId}";
        
        return Cache::remember($cacheKey, function () use ($userId) {
            return Db::name('user')->where('id', $userId)->find();
        }, 600); // 缓存10分钟
    }

    /**
     * 清除用户角色相关缓存
     * @param int $userId 用户ID
     */
    private function clearUserRoleCache(int $userId): void
    {
        Cache::delete("user_roles_{$userId}");
        Cache::delete("user_info_{$userId}");
        
        // 清除角色权限缓存
        foreach (self::ROLE_TYPES as $roleType => $roleName) {
            foreach (self::TARGET_TYPES as $targetType => $targetName) {
                Cache::tag("user_role_{$userId}")->clear();
            }
        }
    }

    /**
     * 批量检查权限
     * @param int $userId 用户ID
     * @param array $permissions 权限列表
     * @return array
     */
    public function checkMultiplePermissions(int $userId, array $permissions): array
    {
        $results = [];
        
        foreach ($permissions as $permission) {
            $type = $permission['type'] ?? 'base';
            $key = $permission['key'] ?? '';
            
            switch ($type) {
                case 'base':
                    $results[$key] = $this->hasBasePermission($userId, $permission['level'] ?? 'verified');
                    break;
                case 'role':
                    $results[$key] = $this->hasRole(
                        $userId,
                        $permission['role_type'],
                        $permission['target_id'] ?? null,
                        $permission['target_type'] ?? null
                    );
                    break;
                case 'group':
                    $results[$key] = $this->canManageGroup($userId, $permission['group_id']);
                    break;
                case 'organization':
                    $results[$key] = $this->canManageOrganization($userId, $permission['organization_id']);
                    break;
            }
        }
        
        return $results;
    }
}
