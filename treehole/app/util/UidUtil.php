<?php
declare(strict_types=1);

namespace app\util;

/**
 * UID工具类
 * 统一处理UID的格式化和相关操作
 */
class UidUtil
{
    /**
     * UID显示位数
     */
    const UID_LENGTH = 6;
    
    /**
     * 格式化UID为指定位数的字符串
     * @param int|null $uid 原始UID
     * @param int $length 位数，默认6位
     * @return string|null 格式化后的UID，如果输入为null则返回null
     */
    public static function format($uid, int $length = self::UID_LENGTH): ?string
    {
        if ($uid === null || $uid === '') {
            return null;
        }
        
        return sprintf('%0' . $length . 'd', (int)$uid);
    }
    
    /**
     * 批量格式化UID数组
     * @param array $uids UID数组
     * @param int $length 位数，默认6位
     * @return array 格式化后的UID数组
     */
    public static function formatBatch(array $uids, int $length = self::UID_LENGTH): array
    {
        return array_map(function($uid) use ($length) {
            return self::format($uid, $length);
        }, $uids);
    }
    
    /**
     * 为用户数据添加格式化UID字段
     * @param array $user 用户数据
     * @param string $sourceField 源字段名，默认'uid'
     * @param string $targetField 目标字段名，默认'uid_formatted'
     * @return array 添加了格式化UID的用户数据
     */
    public static function addFormattedUid(array $user, string $sourceField = 'uid', string $targetField = 'uid_formatted'): array
    {
        $user[$targetField] = self::format($user[$sourceField] ?? null);
        return $user;
    }
    
    /**
     * 为用户数据数组批量添加格式化UID字段
     * @param array $users 用户数据数组
     * @param string $sourceField 源字段名，默认'uid'
     * @param string $targetField 目标字段名，默认'uid_formatted'
     * @return array 添加了格式化UID的用户数据数组
     */
    public static function addFormattedUidBatch(array $users, string $sourceField = 'uid', string $targetField = 'uid_formatted'): array
    {
        return array_map(function($user) use ($sourceField, $targetField) {
            return self::addFormattedUid($user, $sourceField, $targetField);
        }, $users);
    }
    
    /**
     * 验证UID格式是否正确
     * @param mixed $uid 要验证的UID
     * @return bool 是否为有效的UID
     */
    public static function isValid($uid): bool
    {
        if ($uid === null || $uid === '') {
            return true; // null值是允许的
        }
        
        return is_numeric($uid) && (int)$uid > 0;
    }
    
    /**
     * 获取下一个UID（用于分配新UID）
     * @param int $universityId 学校ID
     * @return int 下一个UID
     */
    public static function getNextUid(int $universityId): int
    {
        $counter = \think\facade\Db::table('school_uid_counters')
            ->where('university_id', $universityId)
            ->find();

        if ($counter) {
            $newUid = $counter['current_uid'] + 1;
            \think\facade\Db::table('school_uid_counters')
                ->where('university_id', $universityId)
                ->update(['current_uid' => $newUid]);
        } else {
            $newUid = 1;
            \think\facade\Db::table('school_uid_counters')->insert([
                'university_id' => $universityId,
                'current_uid' => $newUid,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        return $newUid;
    }
    
    /**
     * 为用户分配UID
     * @param int $userId 用户ID
     * @param int $universityId 学校ID
     * @return int|false 分配的UID，失败返回false
     */
    public static function assignToUser(int $userId, int $universityId)
    {
        try {
            return \think\facade\Db::transaction(function() use ($userId, $universityId) {
                $newUid = self::getNextUid($universityId);
                
                $result = \think\facade\Db::table('user')
                    ->where('id', $userId)
                    ->update(['uid' => $newUid]);
                
                if ($result) {
                    \think\facade\Log::info("为用户分配UID成功", [
                        'user_id' => $userId,
                        'university_id' => $universityId,
                        'uid' => $newUid
                    ]);
                    return $newUid;
                }
                
                return false;
            });
        } catch (\Exception $e) {
            \think\facade\Log::error("分配UID失败: " . $e->getMessage(), [
                'user_id' => $userId,
                'university_id' => $universityId
            ]);
            return false;
        }
    }
}
