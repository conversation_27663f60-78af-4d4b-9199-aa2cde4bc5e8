<?php

namespace app\util;

use app\service\PermissionService;
use think\facade\Db;
use think\facade\Log;
use think\Request;

/**
 * 权限管理助手类
 * 提供简化的权限检查方法，类似前端的roleManager
 */
class PermissionHelper
{
    private static $permissionService = null;

    /**
     * 获取权限服务实例
     * @return PermissionService
     */
    private static function getPermissionService(): PermissionService
    {
        if (self::$permissionService === null) {
            self::$permissionService = new PermissionService();
        }
        return self::$permissionService;
    }

    // ========== 基础权限检查 ==========

    /**
     * 检查用户是否有基础权限（已认证）
     * 注意：禁言检查由全局中间件 CheckBanned 处理，这里只检查认证状态
     * @param int $userId 用户ID
     * @return bool
     */
    public static function hasBasePermission(int $userId): bool
    {
        return self::getPermissionService()->hasBasePermission($userId, 'verified');
    }

    /**
     * 检查用户是否有管理员权限
     * @param int $userId 用户ID
     * @return bool
     */
    public static function hasAdminPermission(int $userId): bool
    {
        return self::getPermissionService()->hasBasePermission($userId, '管理员');
    }

    /**
     * 检查用户是否是超级管理员
     * @param int $userId 用户ID
     * @return bool
     */
    public static function isSuperAdmin(int $userId): bool
    {
        return self::getPermissionService()->hasBasePermission($userId, '超级管理员');
    }

    /**
     * 检查用户是否是管理员
     * @param int $userId 用户ID
     * @return bool
     */
    public static function isAdmin(int $userId): bool
    {
        return self::hasAdminPermission($userId);
    }

    // ========== 功能权限检查 ==========

    /**
     * 检查用户是否可以发布活动
     * @param int $userId 用户ID
     * @return bool
     */
    public static function canPublishActivity(int $userId): bool
    {
        // 管理员有发布权限
        if (self::hasAdminPermission($userId)) {
            return true;
        }
        
        // 检查是否有任何组织的管理员权限
        return self::hasSpecificRole($userId, 'club_admin', null, 'organization');
    }

    /**
     * 检查用户是否可以管理公众号
     * @param int $userId 用户ID
     * @param int|null $accountId 公众号ID（可选）
     * @return bool
     */
    public static function canManageOfficialAccounts(int $userId, ?int $accountId = null): bool
    {
        // 管理员有管理权限
        if (self::hasAdminPermission($userId)) {
            return true;
        }
        
        // 检查是否有指定公众号或任何公众号的管理员权限
        return self::hasSpecificRole($userId, 'official_account_admin', $accountId, 'official_account');
    }

    /**
     * 检查用户是否可以管理食堂
     * @param int $userId 用户ID
     * @return bool
     */
    public static function canManageCanteen(int $userId): bool
    {
        // 仅管理员可以管理食堂
        return self::hasAdminPermission($userId);
    }

    /**
     * 检查用户是否是社团管理员
     * @param int $userId 用户ID
     * @param int|null $orgId 组织ID（可选）
     * @return bool
     */
    public static function isClubAdmin(int $userId, ?int $orgId = null): bool
    {
        // 检查是否有指定组织或任何组织的管理员权限
        return self::hasSpecificRole($userId, 'club_admin', $orgId, 'organization');
    }

    // ========== 附加角色权限检查 ==========

    /**
     * 检查用户是否有特定角色
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int|null $targetId 目标ID
     * @param string|null $targetType 目标类型
     * @return bool
     */
    public static function hasSpecificRole(int $userId, string $roleType, ?int $targetId = null, ?string $targetType = null): bool
    {
        return self::getPermissionService()->hasRole($userId, $roleType, $targetId, $targetType);
    }

    /**
     * 检查用户是否可以管理群聊
     * @param int $userId 用户ID
     * @param int $groupId 群聊ID
     * @return bool
     */
    public static function canManageGroup(int $userId, int $groupId): bool
    {
        return self::getPermissionService()->canManageGroup($userId, $groupId);
    }

    /**
     * 检查用户是否可以管理组织
     * @param int $userId 用户ID
     * @param int $orgId 组织ID
     * @return bool
     */
    public static function canManageOrganization(int $userId, int $orgId): bool
    {
        return self::getPermissionService()->canManageOrganization($userId, $orgId);
    }

    /**
     * 检查用户是否可以管理商家
     * @param int $userId 用户ID
     * @param int $businessId 商家ID
     * @return bool
     */
    public static function canManageBusiness(int $userId, int $businessId): bool
    {
        return self::getPermissionService()->canManageBusiness($userId, $businessId);
    }

    // ========== 角色管理 ==========

    /**
     * 获取用户所有角色
     * @param int $userId 用户ID
     * @return array
     */
    public static function getUserRoles(int $userId): array
    {
        return self::getPermissionService()->getUserRoles($userId);
    }

    /**
     * 授予用户角色
     * @param int $targetUserId 目标用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @param int $grantedBy 授权人ID
     * @param string|null $expiresAt 过期时间
     * @return bool
     */
    public static function grantRole(int $targetUserId, string $roleType, int $targetId, string $targetType, int $grantedBy, ?string $expiresAt = null): bool
    {
        return self::getPermissionService()->grantRole($targetUserId, $roleType, $targetId, $targetType, $grantedBy, $expiresAt);
    }

    /**
     * 撤销用户角色
     * @param int $targetUserId 目标用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @return bool
     */
    public static function revokeRole(int $targetUserId, string $roleType, int $targetId, string $targetType): bool
    {
        return self::getPermissionService()->revokeRole($targetUserId, $roleType, $targetId, $targetType);
    }

    // ========== Token验证助手 ==========

    /**
     * 验证请求中的token并返回用户数据
     * @param Request $request 请求对象
     * @return array|null 用户数据，验证失败返回null
     */
    public static function validateRequestToken(Request $request): ?array
    {
        // 从多种方式获取token
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
        }

        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        if (empty($token)) {
            return null;
        }

        try {
            return \app\util\JwtUtil::validateToken($token);
        } catch (\Exception $e) {
            Log::error('Token验证失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 验证token并返回标准的JSON响应
     * @param Request $request 请求对象
     * @return array ['success' => bool, 'user_id' => int, 'response' => array]
     */
    public static function validateTokenWithResponse(Request $request): array
    {
        $userData = self::validateRequestToken($request);

        if (!$userData) {
            return [
                'success' => false,
                'user_id' => 0,
                'response' => ['error_code' => 1, 'msg' => '请先登录']
            ];
        }

        $userId = $userData['user_id'] ?? 0;
        if ($userId <= 0) {
            return [
                'success' => false,
                'user_id' => 0,
                'response' => ['error_code' => 1, 'msg' => 'token中用户信息无效']
            ];
        }

        return [
            'success' => true,
            'user_id' => $userId,
            'response' => null
        ];
    }

    // ========== 权限验证助手 ==========

    /**
     * 检查用户是否有权限执行操作，如果没有则抛出异常
     * @param int $userId 用户ID
     * @param string $permission 权限类型
     * @param mixed ...$args 额外参数
     * @throws \Exception
     */
    public static function requirePermission(int $userId, string $permission, ...$args): void
    {
        $hasPermission = false;
        $errorMessage = '权限不足';

        switch ($permission) {
            case 'base':
                $hasPermission = self::hasBasePermission($userId);
                $errorMessage = '需要完成学生认证';
                break;
            case 'admin':
                $hasPermission = self::hasAdminPermission($userId);
                $errorMessage = '需要管理员权限';
                break;
            case 'publish_activity':
                $hasPermission = self::canPublishActivity($userId);
                $errorMessage = '没有活动发布权限';
                break;
            case 'manage_group':
                $groupId = $args[0] ?? 0;
                $hasPermission = self::canManageGroup($userId, $groupId);
                $errorMessage = '没有群聊管理权限';
                break;
            case 'manage_organization':
                $orgId = $args[0] ?? 0;
                $hasPermission = self::canManageOrganization($userId, $orgId);
                $errorMessage = '没有组织管理权限';
                break;
            case 'manage_business':
                $businessId = $args[0] ?? 0;
                $hasPermission = self::canManageBusiness($userId, $businessId);
                $errorMessage = '没有商家管理权限';
                break;
            default:
                throw new \Exception("未知的权限类型: {$permission}");
        }

        if (!$hasPermission) {
            throw new \Exception($errorMessage);
        }
    }

    /**
     * 检查用户是否有任何管理权限
     * @param int $userId 用户ID
     * @return bool
     */
    public static function hasAnyManagePermission(int $userId): bool
    {
        // 检查基础管理权限
        if (self::hasAdminPermission($userId)) {
            return true;
        }

        // 检查是否有任何附加角色
        $roles = self::getUserRoles($userId);
        return !empty($roles);
    }

    /**
     * 获取用户权限摘要
     * @param int $userId 用户ID
     * @return array
     */
    public static function getUserPermissionSummary(int $userId): array
    {
        $user = Db::name('user')->where('id', $userId)->find();
        $roles = self::getUserRoles($userId);

        return [
            'user_id' => $userId,
            'status' => $user['status'] ?? 'unknown',
            'has_base_permission' => self::hasBasePermission($userId),
            'is_admin' => self::isAdmin($userId),
            'is_super_admin' => self::isSuperAdmin($userId),
            'additional_roles_count' => count($roles),
            'additional_roles' => $roles,
            'can_publish_activity' => self::canPublishActivity($userId),
            'can_manage_official_accounts' => self::canManageOfficialAccounts($userId),
            'can_manage_canteen' => self::canManageCanteen($userId),
            'has_any_manage_permission' => self::hasAnyManagePermission($userId)
        ];
    }
}
