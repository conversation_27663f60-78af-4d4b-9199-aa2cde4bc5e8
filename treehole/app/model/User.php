<?php
namespace app\model;

use think\Model;

class User extends Model
{
    // 设置表名
    protected $name = 'user';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'username'        => 'string',
        'nickname'        => 'string',
        'status'          => 'string',
        'activity_subscribed' => 'int',
        'last_view_activity_time' => 'datetime',
        'avatar'          => 'string',
        'verified_university_id' => 'int',
        'uid'             => 'int',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
    ];
    
    /**
     * 检查用户是否为管理员
     * @return bool
     */
    public function isAdmin()
    {
        // 使用权限助手进行检查
        return \app\util\PermissionHelper::hasAdminPermission($this->id);
    }
    
    /**
     * 关联活动
     */
    public function activities()
    {
        return $this->hasMany(Activity::class, 'user_id');
    }
    
    /**
     * 关联组织管理员
     */
    public function organizationAdmins()
    {
        return $this->hasMany(OrganizationAdmin::class, 'user_id');
    }

    /**
     * 关联用户角色
     */
    public function userRoles()
    {
        return $this->hasMany(UserRole::class, 'user_id');
    }

    /**
     * 获取用户的有效角色
     */
    public function activeRoles()
    {
        return $this->hasMany(UserRole::class, 'user_id')
            ->where('status', 1)
            ->where(function ($query) {
                $query->whereNull('expires_at')->whereOr('expires_at', '>', date('Y-m-d H:i:s'));
            });
    }

    /**
     * 检查用户是否有基础权限
     * @param string $level 权限级别
     * @return bool
     */
    public function hasBasePermission(string $level = 'verified'): bool
    {
        $permissions = [
            'unverified' => 0,
            'verified' => 1,
            '管理员' => 2,
            '超级管理员' => 3,
            '禁言' => -1
        ];

        $userLevel = $permissions[$this->status] ?? 0;
        $requiredLevel = $permissions[$level] ?? 1;

        return $userLevel >= $requiredLevel;
    }

    /**
     * 检查用户是否有特定角色
     * @param string $roleType 角色类型
     * @param int|null $targetId 目标ID
     * @param string|null $targetType 目标类型
     * @return bool
     */
    public function hasRole(string $roleType, ?int $targetId = null, ?string $targetType = null): bool
    {
        return UserRole::hasRole($this->id, $roleType, $targetId, $targetType);
    }
}