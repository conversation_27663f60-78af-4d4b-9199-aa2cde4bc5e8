<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 学校UID计数器模型
 */
class SchoolUidCounter extends Model
{
    // 设置表名
    protected $name = 'school_uid_counters';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'university_id'   => 'int',
        'current_uid'     => 'int',
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];
    
    // 设置时间字段
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 获取下一个UID并更新计数器
     * @param int $universityId 学校ID
     * @return int 新的UID
     */
    public static function getNextUid(int $universityId): int
    {
        // 使用事务确保原子性
        return self::transaction(function() use ($universityId) {
            // 查找或创建计数器记录
            $counter = self::where('university_id', $universityId)->find();
            
            if (!$counter) {
                // 如果不存在，创建新记录
                $counter = new self();
                $counter->university_id = $universityId;
                $counter->current_uid = 1;
                $counter->save();
                return 1;
            } else {
                // 如果存在，递增计数器
                $newUid = $counter->current_uid + 1;
                $counter->current_uid = $newUid;
                $counter->save();
                return $newUid;
            }
        });
    }
    
    /**
     * 获取学校当前的UID计数
     * @param int $universityId 学校ID
     * @return int 当前UID计数
     */
    public static function getCurrentUid(int $universityId): int
    {
        $counter = self::where('university_id', $universityId)->find();
        return $counter ? $counter->current_uid : 0;
    }
}
