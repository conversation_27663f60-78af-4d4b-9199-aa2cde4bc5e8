<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 用户角色模型
 */
class UserRole extends Model
{
    // 设置表名
    protected $name = 'user_roles';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'user_id'         => 'int',
        'role_type'       => 'string',
        'target_id'       => 'int',
        'target_type'     => 'string',
        'granted_by'      => 'int',
        'granted_at'      => 'datetime',
        'expires_at'      => 'datetime',
        'status'          => 'int',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
    ];

    // 角色类型常量
    const ROLE_GROUP_OWNER = 'group_owner';
    const ROLE_GROUP_ADMIN = 'group_admin';
    const ROLE_OFFICIAL_ACCOUNT_ADMIN = 'official_account_admin';
    const ROLE_CLUB_ADMIN = 'club_admin';
    const ROLE_BUSINESS_ADMIN = 'business_admin';
    const ROLE_INSTITUTION_ADMIN = 'institution_admin';

    // 目标类型常量
    const TARGET_GROUP = 'group';
    const TARGET_ORGANIZATION = 'organization';
    const TARGET_OFFICIAL_ACCOUNT = 'official_account';
    const TARGET_BUSINESS = 'business';
    const TARGET_INSTITUTION = 'institution';

    // 状态常量
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联授权人
     */
    public function grantedBy()
    {
        return $this->belongsTo(User::class, 'granted_by');
    }

    /**
     * 关联群聊（当target_type为group时）
     */
    public function group()
    {
        return $this->belongsTo('app\model\Qun', 'target_id')->where('target_type', self::TARGET_GROUP);
    }

    /**
     * 关联组织（当target_type为organization时）
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'target_id')->where('target_type', self::TARGET_ORGANIZATION);
    }

    /**
     * 获取用户的所有有效角色
     * @param int $userId 用户ID
     * @return \think\Collection
     */
    public static function getUserActiveRoles(int $userId)
    {
        return self::where('user_id', $userId)
            ->where('status', self::STATUS_ACTIVE)
            ->where(function ($query) {
                $query->whereNull('expires_at')->whereOr('expires_at', '>', date('Y-m-d H:i:s'));
            })
            ->with(['user', 'grantedBy'])
            ->select();
    }

    /**
     * 检查用户是否有指定角色
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int|null $targetId 目标ID
     * @param string|null $targetType 目标类型
     * @return bool
     */
    public static function hasRole(int $userId, string $roleType, ?int $targetId = null, ?string $targetType = null): bool
    {
        $query = self::where([
            'user_id' => $userId,
            'role_type' => $roleType,
            'status' => self::STATUS_ACTIVE
        ]);

        if ($targetId && $targetType) {
            $query->where([
                'target_id' => $targetId,
                'target_type' => $targetType
            ]);
        }

        // 检查是否过期
        $query->where(function ($query) {
            $query->whereNull('expires_at')->whereOr('expires_at', '>', date('Y-m-d H:i:s'));
        });

        return $query->count() > 0;
    }

    /**
     * 授予角色
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @param int $grantedBy 授权人ID
     * @param string|null $expiresAt 过期时间
     * @return bool
     */
    public static function grantRole(int $userId, string $roleType, int $targetId, string $targetType, int $grantedBy, ?string $expiresAt = null): bool
    {
        try {
            $data = [
                'user_id' => $userId,
                'role_type' => $roleType,
                'target_id' => $targetId,
                'target_type' => $targetType,
                'granted_by' => $grantedBy,
                'granted_at' => date('Y-m-d H:i:s'),
                'expires_at' => $expiresAt,
                'status' => self::STATUS_ACTIVE
            ];

            self::create($data);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 撤销角色
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int $targetId 目标ID
     * @param string $targetType 目标类型
     * @return bool
     */
    public static function revokeRole(int $userId, string $roleType, int $targetId, string $targetType): bool
    {
        try {
            self::where([
                'user_id' => $userId,
                'role_type' => $roleType,
                'target_id' => $targetId,
                'target_type' => $targetType
            ])->update(['status' => self::STATUS_INACTIVE]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取用户在指定目标上的角色
     * @param int $userId 用户ID
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @return \think\Collection
     */
    public static function getUserRolesForTarget(int $userId, string $targetType, int $targetId)
    {
        return self::where([
            'user_id' => $userId,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'status' => self::STATUS_ACTIVE
        ])
        ->where(function ($query) {
            $query->whereNull('expires_at')->whereOr('expires_at', '>', date('Y-m-d H:i:s'));
        })
        ->select();
    }

    /**
     * 获取指定目标的所有管理员
     * @param string $targetType 目标类型
     * @param int $targetId 目标ID
     * @param string|null $roleType 角色类型（可选）
     * @return \think\Collection
     */
    public static function getTargetAdmins(string $targetType, int $targetId, ?string $roleType = null)
    {
        $query = self::where([
            'target_type' => $targetType,
            'target_id' => $targetId,
            'status' => self::STATUS_ACTIVE
        ]);

        if ($roleType) {
            $query->where('role_type', $roleType);
        }

        $query->where(function ($query) {
            $query->whereNull('expires_at')->whereOr('expires_at', '>', date('Y-m-d H:i:s'));
        });

        return $query->with(['user'])->select();
    }

    /**
     * 清理过期角色
     * @return int 清理的数量
     */
    public static function cleanExpiredRoles(): int
    {
        return self::where('expires_at', '<', date('Y-m-d H:i:s'))
            ->where('status', self::STATUS_ACTIVE)
            ->update(['status' => self::STATUS_INACTIVE]);
    }

    /**
     * 获取角色类型中文名称
     * @param string $roleType 角色类型
     * @return string
     */
    public static function getRoleTypeName(string $roleType): string
    {
        $names = [
            self::ROLE_GROUP_OWNER => '群主',
            self::ROLE_GROUP_ADMIN => '群管理员',
            self::ROLE_OFFICIAL_ACCOUNT_ADMIN => '公众号管理员',
            self::ROLE_CLUB_ADMIN => '社团管理员',
            self::ROLE_BUSINESS_ADMIN => '商家管理员',
            self::ROLE_INSTITUTION_ADMIN => '商业机构管理员'
        ];

        return $names[$roleType] ?? $roleType;
    }

    /**
     * 获取目标类型中文名称
     * @param string $targetType 目标类型
     * @return string
     */
    public static function getTargetTypeName(string $targetType): string
    {
        $names = [
            self::TARGET_GROUP => '群聊',
            self::TARGET_ORGANIZATION => '组织',
            self::TARGET_OFFICIAL_ACCOUNT => '公众号',
            self::TARGET_BUSINESS => '商家',
            self::TARGET_INSTITUTION => '商业机构'
        ];

        return $names[$targetType] ?? $targetType;
    }

    /**
     * 获取角色描述
     * @return string
     */
    public function getRoleDescription(): string
    {
        $roleName = self::getRoleTypeName($this->role_type);
        $targetName = self::getTargetTypeName($this->target_type);
        
        // 获取目标名称
        $targetDisplayName = $this->getTargetDisplayName();
        
        return "{$targetDisplayName}的{$roleName}";
    }

    /**
     * 获取目标显示名称
     * @return string
     */
    private function getTargetDisplayName(): string
    {
        switch ($this->target_type) {
            case self::TARGET_GROUP:
                $group = \think\facade\Db::name('qun')->where('id', $this->target_id)->find();
                return $group['quntitle'] ?? "群聊#{$this->target_id}";
                
            case self::TARGET_ORGANIZATION:
                $org = \think\facade\Db::name('organizations')->where('id', $this->target_id)->find();
                return $org['name'] ?? "组织#{$this->target_id}";
                
            default:
                return "{$this->target_type}#{$this->target_id}";
        }
    }
}
