<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

class AuthApplication extends Model
{
    // 表名
    protected $name = 'auth_applications';

    // 主键
    protected $pk = 'id';

    // 字段信息（可选，用于类型约束）
    protected $schema = [
        'id'           => 'int',
        'user_id'      => 'int',
        'username'     => 'string',
        'school_id'    => 'int',
        'school_name'  => 'string',
        'image_url'    => 'string',
        'status'       => 'string',
        'reviewed_by'  => 'int',
        'reviewed_at'  => 'datetime',
        'reason'       => 'string',
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
    ];
}

