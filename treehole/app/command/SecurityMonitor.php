<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\service\system\CacheService;
use app\service\system\AlertService;

class SecurityMonitor extends Command
{
    protected function configure()
    {
        $this->setName('security:monitor')
            ->addOption('clean', null, \think\console\input\Option::VALUE_NONE, '清理过期安全事件')
            ->setDescription('安全监控管理工具');
    }

    protected function execute(Input $input, Output $output)
    {
        // 如果有 --clean 参数，直接执行清理并退出
        if ($input->hasOption('clean') && $input->getOption('clean')) {
            $this->cleanExpiredEvents($output);
            return 0;
        }

        $output->writeln('<info>🛡️ 安全监控管理工具</info>');
        $output->writeln('');

        while (true) {
            $output->writeln('请选择操作：');
            $output->writeln('1. 查看安全事件统计');
            $output->writeln('2. 查看今日安全事件');
            $output->writeln('3. 查看IP黑名单');
            $output->writeln('4. 手动封禁IP');
            $output->writeln('5. 解封IP');
            $output->writeln('6. 清理过期事件');
            $output->writeln('7. 发送测试告警');
            $output->writeln('0. 退出');
            $output->writeln('');
            
            $choice = $this->ask($input, $output, '请输入选项 (0-7): ');
            
            switch ($choice) {
                case '1':
                    $this->showSecurityStats($output);
                    break;
                case '2':
                    $this->showTodayEvents($output);
                    break;
                case '3':
                    $this->showBlacklist($output);
                    break;
                case '4':
                    $this->banIP($input, $output);
                    break;
                case '5':
                    $this->unbanIP($input, $output);
                    break;
                case '6':
                    $this->cleanExpiredEvents($output);
                    break;
                case '7':
                    $this->sendTestAlert($output);
                    break;
                case '0':
                    $output->writeln('<info>再见！</info>');
                    return 0;
                default:
                    $output->writeln('<error>无效选项，请重新选择</error>');
            }
            
            $output->writeln('');
        }
    }
    
    /**
     * 显示安全事件统计
     */
    private function showSecurityStats($output)
    {
        $output->writeln('<info>=== 安全事件统计 ===</info>');
        
        $securityEvents = CacheService::get('security_events', []);
        
        if (empty($securityEvents)) {
            $output->writeln('暂无安全事件记录');
            return;
        }
        
        $totalEvents = 0;
        $totalIPs = 0;
        $todayEvents = 0;
        $today = date('Y-m-d');
        
        foreach ($securityEvents as $date => $dayEvents) {
            foreach ($dayEvents as $ip => $events) {
                $totalEvents += $events['event_count'];
                $totalIPs++;
                
                if ($date === $today) {
                    $todayEvents += $events['event_count'];
                }
            }
        }
        
        $output->writeln("总事件数：{$totalEvents}");
        $output->writeln("涉及IP数：{$totalIPs}");
        $output->writeln("今日事件：{$todayEvents}");
        $output->writeln("记录天数：" . count($securityEvents));
    }
    
    /**
     * 显示今日安全事件
     */
    private function showTodayEvents($output)
    {
        $output->writeln('<info>=== 今日安全事件 ===</info>');
        
        $securityEvents = CacheService::get('security_events', []);
        $today = date('Y-m-d');
        
        if (!isset($securityEvents[$today]) || empty($securityEvents[$today])) {
            $output->writeln('今日暂无安全事件');
            return;
        }
        
        foreach ($securityEvents[$today] as $ip => $events) {
            $output->writeln("IP: {$ip}");
            $output->writeln("  事件数：{$events['event_count']}");
            $output->writeln("  严重度：{$events['total_severity']}");
            $output->writeln("  首次：" . date('H:i:s', $events['first_event']));
            $output->writeln("  最后：" . date('H:i:s', $events['last_event']));
            $output->writeln("  事件：" . implode(', ', array_slice($events['events'], -3)));
            $output->writeln('');
        }
    }
    
    /**
     * 显示IP黑名单
     */
    private function showBlacklist($output)
    {
        $output->writeln('<info>=== IP黑名单 ===</info>');
        
        $blacklist = CacheService::get('ip_blacklist', []);
        
        if (empty($blacklist)) {
            $output->writeln('黑名单为空');
            return;
        }
        
        foreach ($blacklist as $ip => $info) {
            $output->writeln("IP: {$ip}");
            $output->writeln("  违规次数：{$info['offense_count']}");
            $output->writeln("  首次违规：" . date('Y-m-d H:i:s', $info['first_offense']));
            $output->writeln("  最后违规：" . date('Y-m-d H:i:s', $info['last_offense']));
            $output->writeln("  违规原因：" . implode(', ', array_slice($info['reasons'], -2)));
            $output->writeln('');
        }
    }
    
    /**
     * 手动封禁IP
     */
    private function banIP($input, $output)
    {
        $ip = $this->ask($input, $output, '请输入要封禁的IP地址: ');
        $reason = $this->ask($input, $output, '请输入封禁原因: ');
        
        if (empty($ip) || empty($reason)) {
            $output->writeln('<error>IP地址和原因不能为空</error>');
            return;
        }
        
        $blacklist = CacheService::get('ip_blacklist', []);
        $blacklist[$ip] = [
            'first_offense' => time(),
            'last_offense' => time(),
            'offense_count' => 1,
            'reasons' => [$reason],
            'manual_ban' => true
        ];
        
        CacheService::set('ip_blacklist', $blacklist, 86400 * 7); // 保存7天
        
        $output->writeln("<info>✅ IP {$ip} 已被手动封禁</info>");
        
        // 发送封禁通知
        try {
            $alertService = new AlertService();
            $alertService->sendEmail(
                config('secrets.security.admin_email'),
                "IP手动封禁通知",
                "IP {$ip} 已被手动封禁\n原因：{$reason}\n时间：" . date('Y-m-d H:i:s')
            );
        } catch (\Exception $e) {
            $output->writeln('<error>发送通知邮件失败</error>');
        }
    }
    
    /**
     * 解封IP
     */
    private function unbanIP($input, $output)
    {
        $ip = $this->ask($input, $output, '请输入要解封的IP地址: ');
        
        if (empty($ip)) {
            $output->writeln('<error>IP地址不能为空</error>');
            return;
        }
        
        $blacklist = CacheService::get('ip_blacklist', []);
        
        if (!isset($blacklist[$ip])) {
            $output->writeln('<error>该IP不在黑名单中</error>');
            return;
        }
        
        unset($blacklist[$ip]);
        CacheService::set('ip_blacklist', $blacklist, 86400 * 7);
        
        $output->writeln("<info>✅ IP {$ip} 已被解封</info>");
    }
    
    /**
     * 清理过期事件
     */
    private function cleanExpiredEvents($output)
    {
        $output->writeln('<info>正在清理过期安全事件...</info>');
        
        $securityEvents = CacheService::get('security_events', []);
        $sevenDaysAgo = date('Y-m-d', time() - 7 * 24 * 3600);
        $cleanedDays = 0;
        
        foreach ($securityEvents as $date => $events) {
            if ($date < $sevenDaysAgo) {
                unset($securityEvents[$date]);
                $cleanedDays++;
            }
        }
        
        CacheService::set('security_events', $securityEvents, 86400 * 7);
        
        $output->writeln("✅ 已清理 {$cleanedDays} 天的过期事件");
    }
    
    /**
     * 发送测试告警
     */
    private function sendTestAlert($output)
    {
        $output->writeln('<info>正在发送测试告警...</info>');
        
        try {
            $alertService = new AlertService();
            $alertService->sendEmail(
                config('secrets.security.admin_email'),
                '🧪 安全监控测试告警',
                "这是一封测试告警邮件\n\n发送时间：" . date('Y-m-d H:i:s') . "\n系统状态：正常"
            );
            
            $output->writeln('<info>✅ 测试告警已发送</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>❌ 发送失败：' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 询问用户输入
     */
    private function ask($input, $output, $question)
    {
        $output->write($question);
        return trim(fgets(STDIN));
    }
}
