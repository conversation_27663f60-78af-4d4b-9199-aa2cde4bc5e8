<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\service\system\WeeklyReportService;

class WeeklyReport extends Command
{
    protected function configure()
    {
        $this->setName('report:weekly')
            ->setDescription('生成并发送安全监控周报');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('<info>开始生成安全监控周报...</info>');
        
        $reportService = new WeeklyReportService();
        $result = $reportService->generateAndSendWeeklyReport();
        
        if ($result) {
            $output->writeln('<info>✅ 周报生成并发送成功！</info>');
            $output->writeln('📧 报告已发送到管理员邮箱：' . config('secrets.security.admin_email'));
        } else {
            $output->writeln('<error>❌ 周报生成失败，请检查日志</error>');
        }
        
        return $result ? 0 : 1;
    }
}
