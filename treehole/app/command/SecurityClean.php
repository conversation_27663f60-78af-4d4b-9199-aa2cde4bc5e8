<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class SecurityClean extends Command
{
    protected function configure()
    {
        $this->setName('security:clean')
            ->setDescription('清理恶意用户名和数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('<info>开始清理恶意用户名...</info>');
        
        // 恶意关键词列表（只检测真正的SQL注入关键词）
        $maliciousKeywords = [
            'union select', 'union all select', 'or 1=1', 'and 1=1', 'or 1 = 1', 'and 1 = 1',
            'information_schema', 'mysql.user', 'drop table', 'delete from', 'insert into',
            'update set', 'exec(', 'execute(', 'sp_executesql', 'xp_cmdshell'
        ];

        // 真正危险的字符组合（避免误伤正常特殊字符）
        $dangerousPatterns = [
            "/'/", // 单引号
            '/"/', // 双引号  
            '/;/', // 分号
            '/--/', // SQL注释
            '/\/\*/', // 多行注释开始
            '/\*\//', // 多行注释结束
            '/<script/i', // script标签
            '/<\/script>/i', // script结束标签
        ];

        try {
            // 查找包含恶意内容的用户
            $maliciousUsers = [];
            
            // 检查恶意关键词组合
            foreach ($maliciousKeywords as $keyword) {
                $users = Db::name('user')
                    ->where('username', 'like', "%{$keyword}%")
                    ->field('id, username, openid')
                    ->select();
                
                foreach ($users as $user) {
                    $maliciousUsers[$user['id']] = $user;
                }
            }
            
            // 检查危险字符模式
            $allUsers = Db::name('user')
                ->field('id, username, openid')
                ->select();
            
            foreach ($allUsers as $user) {
                foreach ($dangerousPatterns as $pattern) {
                    if (preg_match($pattern, $user['username'])) {
                        $maliciousUsers[$user['id']] = $user;
                        break;
                    }
                }
            }
            
            $output->writeln("发现 " . count($maliciousUsers) . " 个恶意用户名");
            
            if (empty($maliciousUsers)) {
                $output->writeln('<info>没有发现恶意用户名，清理完成。</info>');
                return 0;
            }
            
            // 显示恶意用户列表
            $output->writeln("\n<comment>恶意用户列表：</comment>");
            $output->writeln("ID\t用户名");
            $output->writeln("----------------------------------------");
            foreach ($maliciousUsers as $user) {
                $output->writeln($user['id'] . "\t" . $user['username']);
            }
            
            $output->writeln("\n<question>是否继续清理这些用户名？(y/n):</question> ");
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (trim($line) !== 'y') {
                $output->writeln('<info>取消清理操作。</info>');
                return 0;
            }
            
            // 开始清理
            $cleanedCount = 0;
            foreach ($maliciousUsers as $user) {
                // 生成新的安全用户名
                $newUsername = '航友' . substr(md5($user['openid']), 0, 8);
                
                // 确保用户名唯一
                $counter = 1;
                $originalNewUsername = $newUsername;
                while (Db::name('user')->where('username', $newUsername)->find()) {
                    $newUsername = $originalNewUsername . $counter;
                    $counter++;
                }
                
                // 更新用户名
                Db::startTrans();
                try {
                    // 更新user表
                    Db::name('user')->where('id', $user['id'])->update(['username' => $newUsername]);
                    
                    // 更新message表
                    Db::name('message')->where('user_id', $user['id'])->update(['username' => $newUsername]);
                    
                    // 更新comment表
                    Db::name('comment')->where('user_id', $user['id'])->update(['username' => $newUsername]);
                    
                    // 更新post表
                    Db::name('post')->where('user_id', $user['id'])->update(['username' => $newUsername]);
                    
                    Db::commit();
                    
                    $output->writeln("用户 {$user['id']} 的用户名从 '{$user['username']}' 更新为 '{$newUsername}'");
                    $cleanedCount++;
                    
                } catch (\Exception $e) {
                    Db::rollback();
                    $output->writeln("<error>更新用户 {$user['id']} 失败: " . $e->getMessage() . "</error>");
                }
            }
            
            $output->writeln("\n<info>清理完成！共清理了 {$cleanedCount} 个恶意用户名。</info>");
            
            // 记录日志
            Log::info('恶意用户名清理完成', [
                'total_found' => count($maliciousUsers),
                'cleaned_count' => $cleanedCount,
                'cleaned_users' => array_keys($maliciousUsers)
            ]);
            
            return 0;
            
        } catch (\Exception $e) {
            $output->writeln("<error>清理过程中发生错误: " . $e->getMessage() . "</error>");
            Log::error('恶意用户名清理失败', ['error' => $e->getMessage()]);
            return 1;
        }
    }
}
