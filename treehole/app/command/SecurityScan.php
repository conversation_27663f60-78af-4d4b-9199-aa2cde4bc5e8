<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use app\service\system\CacheService;
use app\service\system\DatabaseAdapter;
use app\service\system\AlertService;

class SecurityScan extends Command
{
    protected function configure()
    {
        $this->setName('security:scan')
            ->setDescription('执行安全扫描，检查恶意数据和异常行为');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行安全扫描...');

        // 检查数据库结构变更
        DatabaseAdapter::checkStructureChanges();

        // 清理过期的文件缓存
        CacheService::cleanExpiredFileCache();

        $results = [
            'malicious_users' => $this->scanMaliciousUsers(),
            'suspicious_posts' => $this->scanSuspiciousPosts(),
            'abnormal_requests' => $this->scanAbnormalRequests(),
            'failed_logins' => $this->scanFailedLogins(),
            'cache_stats' => CacheService::getCacheStats(),
            'db_structure' => DatabaseAdapter::generateStructureReport(),
        ];

        // 生成扫描报告
        $this->generateReport($results);

        // 发送告警（如果有问题）
        $this->sendAlerts($results);

        $output->writeln('安全扫描完成！');
        return 0;
    }
    
    /**
     * 扫描恶意用户名
     */
    private function scanMaliciousUsers()
    {
        $maliciousKeywords = [
            'union select', 'union all select', 'or 1=1', 'and 1=1',
            'drop table', 'delete from', 'insert into', 'update set',
            'script', 'javascript', 'eval', 'exec'
        ];
        
        $dangerousChars = ["'", '"', ';', '--', '/*', '*/', '<script'];
        
        $maliciousUsers = [];
        
        // 检查user表是否存在
        if (!DatabaseAdapter::tableExists('user')) {
            return $maliciousUsers;
        }

        // 获取安全的字段列表
        $safeFields = DatabaseAdapter::buildSafeFields('user', ['id', 'username', 'openid']);
        if (empty($safeFields)) {
            return $maliciousUsers;
        }

        // 检查恶意关键词组合
        foreach ($maliciousKeywords as $keyword) {
            try {
                $users = Db::name('user')
                    ->where('username', 'like', "%{$keyword}%")
                    ->field(implode(', ', $safeFields))
                    ->select();

                foreach ($users as $user) {
                    $maliciousUsers[] = [
                        'table' => 'user',
                        'id' => $user['id'],
                        'username' => $user['username'] ?? '',
                        'reason' => "包含关键词: {$keyword}",
                        'openid' => $user['openid'] ?? ''
                    ];
                }
            } catch (\Exception $e) {
                Log::warning("扫描恶意关键词失败: {$keyword}", ['error' => $e->getMessage()]);
            }
        }

        // 检查危险字符
        try {
            $allUsers = Db::name('user')->field(implode(', ', $safeFields))->select();
            foreach ($allUsers as $user) {
                foreach ($dangerousChars as $char) {
                    if (stripos($user['username'] ?? '', $char) !== false) {
                        $maliciousUsers[] = [
                            'table' => 'user',
                            'id' => $user['id'],
                            'username' => $user['username'] ?? '',
                            'reason' => "包含危险字符: {$char}",
                            'openid' => $user['openid'] ?? ''
                        ];
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('扫描危险字符失败', ['error' => $e->getMessage()]);
        }
        
        return $maliciousUsers;
    }
    
    /**
     * 扫描可疑帖子内容
     */
    private function scanSuspiciousPosts()
    {
        $suspiciousPosts = [];

        // 检查message表是否存在
        if (!DatabaseAdapter::tableExists('message')) {
            return $suspiciousPosts;
        }

        // 获取时间字段和内容字段
        $timeField = DatabaseAdapter::getTimeField('message');
        $contentField = DatabaseAdapter::getContentField('message');
        $userIdField = DatabaseAdapter::getUserIdField('message');

        if (!$timeField || !$contentField) {
            Log::warning('message表缺少必要字段', [
                'time_field' => $timeField,
                'content_field' => $contentField
            ]);
            return $suspiciousPosts;
        }

        // 构建安全的字段列表
        $requestedFields = ['id', $contentField, $userIdField, $timeField];
        $safeFields = DatabaseAdapter::buildSafeFields('message', $requestedFields);

        // 构建时间范围条件
        $timeCondition = DatabaseAdapter::buildTimeRangeCondition('message', 7);

        try {
            $query = Db::name('message')->field(implode(', ', $safeFields));

            if (!empty($timeCondition)) {
                $query = $query->where($timeCondition[0], $timeCondition[1], $timeCondition[2]);
            }

            $posts = $query->select();

            foreach ($posts as $post) {
                $content = $post[$contentField] ?? '';
                if ($this->containsSuspiciousContent($content)) {
                    $suspiciousPosts[] = [
                        'table' => 'message',
                        'id' => $post['id'],
                        'user_id' => $post[$userIdField] ?? '',
                        'content' => substr($content, 0, 100) . '...',
                        'time_field' => $timeField,
                        'time_value' => $post[$timeField] ?? '',
                        'formatted_time' => $this->formatTime($post[$timeField] ?? '', $timeField)
                    ];
                }
            }
        } catch (\Exception $e) {
            Log::warning('扫描可疑帖子失败', ['error' => $e->getMessage()]);
        }

        return $suspiciousPosts;
    }

    /**
     * 格式化时间显示
     */
    private function formatTime($timeValue, $timeField)
    {
        if (empty($timeValue)) {
            return '';
        }

        // 如果是时间戳
        if (is_numeric($timeValue)) {
            return date('Y-m-d H:i:s', $timeValue);
        }

        // 如果已经是日期时间格式
        return $timeValue;
    }
    
    /**
     * 扫描异常请求
     */
    private function scanAbnormalRequests()
    {
        $logFile = app()->getRootPath() . 'logs/php_error.log';
        $abnormalRequests = [];
        
        if (file_exists($logFile)) {
            $logs = file($logFile);
            $recentLogs = array_slice($logs, -1000); // 最近1000行日志
            
            foreach ($recentLogs as $log) {
                if (strpos($log, 'SQL注入攻击尝试') !== false) {
                    $abnormalRequests[] = [
                        'type' => 'SQL注入尝试',
                        'log' => trim($log),
                        'time' => date('Y-m-d H:i:s')
                    ];
                }
            }
        }
        
        return $abnormalRequests;
    }
    
    /**
     * 扫描登录失败记录
     */
    private function scanFailedLogins()
    {
        // 从缓存中获取失败登录记录
        $failedLogins = CacheService::get('failed_logins', []);

        // 统计最近24小时的失败登录
        $recentFailed = [];
        $yesterday = time() - 24*3600;

        foreach ($failedLogins as $ip => $attempts) {
            $recentAttempts = array_filter($attempts, function($time) use ($yesterday) {
                return $time > $yesterday;
            });

            if (count($recentAttempts) > 10) { // 24小时内失败超过10次
                $recentFailed[] = [
                    'ip' => $ip,
                    'attempts' => count($recentAttempts),
                    'last_attempt' => date('Y-m-d H:i:s', max($recentAttempts))
                ];
            }
        }

        return $recentFailed;
    }
    
    /**
     * 检查内容是否可疑
     */
    private function containsSuspiciousContent($content)
    {
        $suspiciousPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/<script/i',
            '/javascript:/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 生成扫描报告
     */
    private function generateReport($results)
    {
        $report = [
            'scan_time' => date('Y-m-d H:i:s'),
            'malicious_users_count' => count($results['malicious_users']),
            'suspicious_posts_count' => count($results['suspicious_posts']),
            'abnormal_requests_count' => count($results['abnormal_requests']),
            'failed_logins_count' => count($results['failed_logins']),
            'details' => $results
        ];
        
        // 保存报告到文件
        $reportFile = app()->getRootPath() . 'logs/security_scan_' . date('Y-m-d') . '.json';
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        // 记录日志
        Log::info('安全扫描完成', $report);
    }
    
    /**
     * 发送告警
     */
    private function sendAlerts($results)
    {
        $totalIssues = count($results['malicious_users']) + 
                      count($results['suspicious_posts']) + 
                      count($results['abnormal_requests']) + 
                      count($results['failed_logins']);
        
        if ($totalIssues > 0) {
            // 记录高级别日志
            Log::warning('安全扫描发现问题', [
                'total_issues' => $totalIssues,
                'malicious_users' => count($results['malicious_users']),
                'suspicious_posts' => count($results['suspicious_posts']),
                'abnormal_requests' => count($results['abnormal_requests']),
                'failed_logins' => count($results['failed_logins'])
            ]);
            
            // 这里可以添加邮件通知、短信通知等
            // $this->sendEmailAlert($results);
        }
    }
}
