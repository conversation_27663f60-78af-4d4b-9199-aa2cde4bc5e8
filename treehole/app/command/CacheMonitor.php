<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\service\system\CacheService;
use app\service\system\AlertService;
use think\facade\Log;

class CacheMonitor extends Command
{
    protected function configure()
    {
        $this->setName('cache:monitor')
            ->setDescription('监控缓存系统状态');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('<info>开始监控缓存系统状态...</info>');
        
        // 获取缓存统计信息
        $stats = CacheService::getCacheStats();
        
        $output->writeln('=== 缓存系统状态 ===');
        $output->writeln("Redis可用性: " . ($stats['redis_available'] ? '✅ 正常' : '❌ 不可用'));
        $output->writeln("当前缓存类型: " . $stats['cache_type']);
        $output->writeln("Redis失败次数: " . $stats['redis_failure_count']);
        $output->writeln("最后检查时间: " . $stats['last_check_time']);
        $output->writeln("文件缓存数量: " . $stats['file_cache_count']);
        
        // 清理过期的文件缓存
        $output->writeln('');
        $output->writeln('<info>清理过期文件缓存...</info>');
        CacheService::cleanExpiredFileCache();
        
        // 测试缓存读写
        $output->writeln('');
        $output->writeln('<info>测试缓存读写...</info>');
        $this->testCacheOperations($output);
        
        // 检查Redis长时间不可用情况
        $this->checkLongTermRedisFailure($output);
        
        $output->writeln('');
        $output->writeln('<info>缓存监控完成！</info>');
        return 0;
    }
    
    /**
     * 测试缓存操作
     */
    private function testCacheOperations($output)
    {
        $testKey = 'cache_monitor_test_' . time();
        $testValue = 'test_value_' . rand(1000, 9999);
        
        try {
            // 测试写入
            $setResult = CacheService::set($testKey, $testValue, 60);
            if ($setResult) {
                $output->writeln('✅ 缓存写入测试成功');
            } else {
                $output->writeln('❌ 缓存写入测试失败');
            }
            
            // 测试读取
            $getValue = CacheService::get($testKey);
            if ($getValue === $testValue) {
                $output->writeln('✅ 缓存读取测试成功');
            } else {
                $output->writeln('❌ 缓存读取测试失败');
            }
            
            // 测试删除
            $deleteResult = CacheService::delete($testKey);
            if ($deleteResult) {
                $output->writeln('✅ 缓存删除测试成功');
            } else {
                $output->writeln('❌ 缓存删除测试失败');
            }
            
        } catch (\Exception $e) {
            $output->writeln('<error>缓存操作测试异常: ' . $e->getMessage() . '</error>');
            Log::error('缓存操作测试异常', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 检查Redis长时间不可用
     */
    private function checkLongTermRedisFailure($output)
    {
        $redisDownKey = 'redis_down_since';
        $currentTime = time();
        
        if (!CacheService::isRedisAvailable()) {
            // Redis不可用，记录开始时间
            $downSince = CacheService::get($redisDownKey);
            
            if (!$downSince) {
                // 第一次检测到Redis不可用
                CacheService::set($redisDownKey, $currentTime, 86400 * 7); // 保存7天
                $output->writeln('⚠️  检测到Redis不可用，开始计时');
                Log::warning('Redis服务不可用，开始监控恢复时间');
            } else {
                // 计算不可用时长
                $downDuration = $currentTime - $downSince;
                $downHours = round($downDuration / 3600, 1);
                
                $output->writeln("⚠️  Redis已不可用 {$downHours} 小时");
                
                // 如果超过2小时，发送告警
                if ($downDuration > 2 * 3600) {
                    $this->sendLongTermFailureAlert($downHours);
                }
            }
        } else {
            // Redis可用，清除记录
            $downSince = CacheService::get($redisDownKey);
            if ($downSince) {
                $downDuration = $currentTime - $downSince;
                $downHours = round($downDuration / 3600, 1);
                
                CacheService::delete($redisDownKey);
                $output->writeln("✅ Redis已恢复正常（曾不可用 {$downHours} 小时）");
                
                // 发送恢复通知
                $this->sendRecoveryAlert($downHours);
            }
        }
    }
    
    /**
     * 发送长时间故障告警
     */
    private function sendLongTermFailureAlert($downHours)
    {
        // 避免重复发送告警
        $alertKey = 'redis_long_failure_alert_' . date('Y-m-d-H');
        if (CacheService::get($alertKey)) {
            return;
        }
        
        CacheService::set($alertKey, true, 3600); // 1小时内不重复发送
        
        try {
            $alertService = new AlertService();
            $data = [
                'down_hours' => $downHours,
                'message' => "Redis服务已不可用 {$downHours} 小时",
                'current_cache' => '文件缓存',
                'impact' => '系统性能可能受到影响',
                'suggestion' => '请尽快检查并修复Redis服务',
                'time' => date('Y-m-d H:i:s')
            ];
            
            $alertService->sendRealTimeAlert('Redis长时间故障', $data);
            Log::error("Redis长时间不可用告警", $data);
            
        } catch (\Exception $e) {
            Log::error('发送Redis长时间故障告警失败', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 发送恢复通知
     */
    private function sendRecoveryAlert($downHours)
    {
        try {
            $alertService = new AlertService();
            $data = [
                'down_hours' => $downHours,
                'message' => "Redis服务已恢复正常",
                'recovery_time' => date('Y-m-d H:i:s'),
                'downtime_duration' => "{$downHours} 小时"
            ];
            
            $alertService->sendRealTimeAlert('Redis服务恢复', $data);
            Log::info("Redis服务恢复通知", $data);
            
        } catch (\Exception $e) {
            Log::error('发送Redis恢复通知失败', ['error' => $e->getMessage()]);
        }
    }
}
