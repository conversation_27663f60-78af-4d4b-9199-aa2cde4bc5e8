<?php
namespace app\middleware;

use think\facade\Cache;
use think\facade\Log;
use think\Request;
use app\service\system\CacheService;

class SecurityMonitor
{
    public function handle(Request $request, \Closure $next)
    {
        $ip = $request->ip();
        $userAgent = $request->header('User-Agent', '');
        $requestUri = $request->url();
        $method = $request->method();
        
        // 检查请求频率
        $this->checkRequestFrequency($ip);
        
        // 检查可疑User-Agent
        $this->checkSuspiciousUserAgent($ip, $userAgent);
        
        // 检查可疑请求路径
        $this->checkSuspiciousPath($ip, $requestUri);
        
        // 记录请求信息
        $this->logRequest($ip, $method, $requestUri, $userAgent);
        
        return $next($request);
    }
    
    /**
     * 检查请求频率
     */
    private function checkRequestFrequency($ip)
    {
        // 跳过本地开发环境
        if ($this->isLocalDevelopment($ip)) {
            return;
        }

        $key = "request_freq_{$ip}";
        $requests = CacheService::get($key, []);
        $now = time();

        // 清理1分钟前的记录
        $requests = array_filter($requests, function($time) use ($now) {
            return $now - $time < 60;
        });

        $requests[] = $now;
        CacheService::set($key, $requests, 300); // 缓存5分钟

        $requestCount = count($requests);

        // 从配置获取阈值
        $maxRequests = config('secrets.security.max_requests_per_minute', 100);
        $criticalThreshold = $maxRequests * 2; // 严重告警阈值为配置值的2倍
        $warningThreshold = $maxRequests;      // 警告阈值为配置值
        $infoThreshold = $maxRequests / 2;     // 信息阈值为配置值的一半

        // 多级告警机制 - 只告警不封禁
        if ($requestCount > $criticalThreshold) {
            // 严重告警
            Log::error('严重高频请求告警', [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'threshold' => $criticalThreshold,
                'time' => date('Y-m-d H:i:s'),
                'level' => 'critical'
            ]);

            // 只记录到监控日志，不加入黑名单
            $this->recordSecurityEvent($ip, '严重高频请求', $requestCount);
            $this->sendHighFrequencyAlert($ip, $requestCount, 'critical');

        } elseif ($requestCount > $warningThreshold) {
            // 警告
            Log::warning('高频请求告警', [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'threshold' => $warningThreshold,
                'time' => date('Y-m-d H:i:s'),
                'level' => 'warning'
            ]);

            // 只记录到监控日志，不加入黑名单
            $this->recordSecurityEvent($ip, '高频请求', $requestCount);
            $this->sendHighFrequencyAlert($ip, $requestCount, 'warning');

        } elseif ($requestCount > $infoThreshold) {
            // 注意
            Log::info('可疑请求频率', [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'threshold' => $infoThreshold,
                'time' => date('Y-m-d H:i:s'),
                'level' => 'info'
            ]);
        }
    }
    
    /**
     * 检查可疑User-Agent
     */
    private function checkSuspiciousUserAgent($ip, $userAgent)
    {
        // 跳过本地开发环境
        if ($this->isLocalDevelopment($ip)) {
            return;
        }

        // 跳过微信开发者工具
        if ($this->isWechatDevTool($userAgent)) {
            return;
        }

        $suspiciousAgents = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'nessus',
            'burpsuite', 'owasp', 'w3af', 'acunetix',
            'python-requests', 'curl', 'wget'
        ];

        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                Log::warning('可疑User-Agent告警', [
                    'ip' => $ip,
                    'user_agent' => $userAgent,
                    'suspicious_keyword' => $agent,
                    'time' => date('Y-m-d H:i:s')
                ]);

                // 只记录事件，不加入黑名单
                $this->recordSecurityEvent($ip, "可疑User-Agent: {$agent}", 1);
                break;
            }
        }
    }
    
    /**
     * 检查可疑请求路径
     */
    private function checkSuspiciousPath($ip, $path)
    {
        $suspiciousPaths = [
            '/admin', '/phpmyadmin', '/wp-admin', '/wp-login',
            '/.env', '/config', '/backup', '/database',
            '/shell', '/cmd', '/eval', '/system'
        ];
        
        foreach ($suspiciousPaths as $suspiciousPath) {
            if (stripos($path, $suspiciousPath) !== false) {
                Log::warning('可疑路径访问告警', [
                    'ip' => $ip,
                    'path' => $path,
                    'suspicious_path' => $suspiciousPath,
                    'time' => date('Y-m-d H:i:s')
                ]);

                // 只记录事件，不加入黑名单
                $this->recordSecurityEvent($ip, "访问可疑路径: {$suspiciousPath}", 1);
                break;
            }
        }
    }
    
    /**
     * 记录请求信息
     */
    private function logRequest($ip, $method, $uri, $userAgent)
    {
        // 记录到缓存中，用于后续分析
        $key = "request_log_" . date('Y-m-d-H');
        $logs = Cache::get($key, []);
        
        $logs[] = [
            'ip' => $ip,
            'method' => $method,
            'uri' => $uri,
            'user_agent' => $userAgent,
            'time' => time()
        ];
        
        // 只保留最近1000条记录
        if (count($logs) > 1000) {
            $logs = array_slice($logs, -1000);
        }
        
        Cache::set($key, $logs, 3600); // 缓存1小时
    }
    
    /**
     * 记录安全事件（不封禁，只记录）
     */
    private function recordSecurityEvent($ip, $reason, $severity = 1)
    {
        $securityEvents = CacheService::get('security_events', []);
        $today = date('Y-m-d');

        if (!isset($securityEvents[$today])) {
            $securityEvents[$today] = [];
        }

        if (!isset($securityEvents[$today][$ip])) {
            $securityEvents[$today][$ip] = [
                'first_event' => time(),
                'last_event' => time(),
                'event_count' => 1,
                'total_severity' => $severity,
                'events' => [$reason]
            ];
        } else {
            $securityEvents[$today][$ip]['last_event'] = time();
            $securityEvents[$today][$ip]['event_count']++;
            $securityEvents[$today][$ip]['total_severity'] += $severity;
            $securityEvents[$today][$ip]['events'][] = $reason;
        }

        // 保存7天的安全事件记录
        CacheService::set('security_events', $securityEvents, 86400 * 7);

        // 如果单个IP当天事件过多，发送汇总告警
        $ipEvents = $securityEvents[$today][$ip];
        if ($ipEvents['event_count'] >= 10 && $ipEvents['event_count'] % 10 == 0) {
            $this->sendSecuritySummaryAlert($ip, $ipEvents);
        }
    }

    /**
     * 添加到黑名单（保留方法，但默认不使用）
     */
    private function addToBlacklist($ip, $reason)
    {
        // 生产环境建议只记录，不自动封禁
        $this->recordSecurityEvent($ip, $reason, 5);

        // 如果需要手动启用封禁功能，取消下面的注释
        /*
        $blacklist = CacheService::get('ip_blacklist', []);

        if (!isset($blacklist[$ip])) {
            $blacklist[$ip] = [
                'first_offense' => time(),
                'last_offense' => time(),
                'offense_count' => 1,
                'reasons' => [$reason]
            ];
        } else {
            $blacklist[$ip]['last_offense'] = time();
            $blacklist[$ip]['offense_count']++;
            $blacklist[$ip]['reasons'][] = $reason;
        }

        CacheService::set('ip_blacklist', $blacklist, 86400); // 缓存24小时

        Log::error('IP已加入黑名单', [
            'ip' => $ip,
            'reason' => $reason,
            'time' => date('Y-m-d H:i:s')
        ]);
        */
    }

    /**
     * 发送高频请求告警邮件
     */
    private function sendHighFrequencyAlert($ip, $requestCount, $level)
    {
        // 防止同一IP短时间内重复发送告警
        $alertKey = "alert_sent_freq_{$ip}";
        if (CacheService::get($alertKey)) {
            return;
        }

        CacheService::set($alertKey, true, 300); // 5分钟内不重复发送

        try {
            // 使用现有的邮件发送服务
            $alertService = new \app\service\AlertService();
            $data = [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'level' => $level,
                'time' => date('Y-m-d H:i:s'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown'
            ];

            $alertService->sendRealTimeAlert('高频请求检测', $data);

        } catch (\Exception $e) {
            Log::error('发送高频请求告警失败', [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 使用文件缓存检查请求频率（Redis不可用时的备选方案）
     */
    private function checkRequestFrequencyWithFile($ip)
    {
        $cacheDir = app()->getRuntimePath() . 'security_cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        $cacheFile = $cacheDir . '/freq_' . md5($ip) . '.json';
        $now = time();

        // 读取现有记录
        $requests = [];
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data && isset($data['requests'])) {
                $requests = $data['requests'];
            }
        }

        // 清理1分钟前的记录
        $requests = array_filter($requests, function($time) use ($now) {
            return $now - $time < 60;
        });

        $requests[] = $now;

        // 保存到文件
        file_put_contents($cacheFile, json_encode(['requests' => $requests, 'updated' => $now]));

        $requestCount = count($requests);

        // 检查频率（与Redis版本相同的逻辑）
        $maxRequests = config('secrets.security.max_requests_per_minute', 100);
        $criticalThreshold = $maxRequests * 2;
        $warningThreshold = $maxRequests;
        $infoThreshold = $maxRequests / 2;

        if ($requestCount > $criticalThreshold) {
            Log::error('严重高频请求告警（文件缓存）', [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'threshold' => $criticalThreshold,
                'time' => date('Y-m-d H:i:s'),
                'level' => 'critical'
            ]);
            $this->sendHighFrequencyAlert($ip, $requestCount, 'critical');

        } elseif ($requestCount > $warningThreshold) {
            Log::warning('高频请求告警（文件缓存）', [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'threshold' => $warningThreshold,
                'time' => date('Y-m-d H:i:s'),
                'level' => 'warning'
            ]);
            $this->sendHighFrequencyAlert($ip, $requestCount, 'warning');

        } elseif ($requestCount > $infoThreshold) {
            Log::info('可疑请求频率（文件缓存）', [
                'ip' => $ip,
                'requests_per_minute' => $requestCount,
                'threshold' => $infoThreshold,
                'time' => date('Y-m-d H:i:s'),
                'level' => 'info'
            ]);
        }
    }

    /**
     * 判断是否为本地开发环境
     */
    private function isLocalDevelopment($ip)
    {
        $localIps = [
            '127.0.0.1',
            '::1',
            'localhost',
            '0.0.0.0'
        ];

        return in_array($ip, $localIps);
    }

    /**
     * 判断是否为微信开发者工具
     */
    private function isWechatDevTool($userAgent)
    {
        $wechatDevKeywords = [
            'wechatdevtools',
            'MicroMessenger',
            'miniProgram'
        ];

        foreach ($wechatDevKeywords as $keyword) {
            if (stripos($userAgent, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 发送安全事件汇总告警
     */
    private function sendSecuritySummaryAlert($ip, $events)
    {
        try {
            $alertService = new \app\service\system\AlertService();

            $subject = "🚨 安全事件汇总告警 - IP: {$ip}";
            $content = "检测到IP {$ip} 在今日发生多次安全事件：\n\n";
            $content .= "事件总数：{$events['event_count']}\n";
            $content .= "严重程度：{$events['total_severity']}\n";
            $content .= "首次事件：" . date('Y-m-d H:i:s', $events['first_event']) . "\n";
            $content .= "最后事件：" . date('Y-m-d H:i:s', $events['last_event']) . "\n\n";
            $content .= "事件详情：\n";

            foreach ($events['events'] as $index => $event) {
                $content .= ($index + 1) . ". {$event}\n";
            }

            $content .= "\n建议：请检查该IP的行为是否正常，必要时可手动封禁。";

            $alertService->sendEmail(
                config('secrets.security.admin_email'),
                $subject,
                $content
            );

        } catch (\Exception $e) {
            Log::error('发送安全事件汇总告警失败', ['error' => $e->getMessage()]);
        }
    }
}
