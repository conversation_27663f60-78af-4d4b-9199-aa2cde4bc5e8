<?php

namespace app\middleware;

use app\util\JwtUtil;
use think\facade\Db;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 禁言检查中间件
 * 在所有需要登录的请求前检查用户是否被禁言
 */
class CheckBanned
{
    /**
     * 不需要检查禁言的路由白名单
     * 禁言用户只能访问登录相关和获取自己状态的接口
     * @var array
     */
    private $whitelist = [
        // 登录相关 - 必须保留
        '/user/login',
        '/user/register',
        '/user/wechatLogin',
        '/user/refreshToken',

        // 获取自己的状态信息 - 让用户知道自己被禁言
        '/user/verifyStatus',
        '/user/getUserInfo'
    ];

    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取当前请求路径
        $path = $request->pathinfo();
        
        // 检查是否在白名单中
        if ($this->isInWhitelist($path)) {
            return $next($request);
        }

        // 获取token
        $token = $request->header('token');
        if (!$token) {
            // 没有token的请求直接放行，由其他中间件处理
            return $next($request);
        }

        try {
            // 验证token并获取用户信息
            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                // token无效，由其他中间件处理
                return $next($request);
            }

            $userId = $userData['user_id'];
            
            // 查询用户状态
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                return json([
                    'error_code' => 404,
                    'msg' => '用户不存在'
                ]);
            }

            // 检查是否被禁言
            if ($user['status'] === '禁言') {
                Log::warning("禁言用户尝试访问: 用户ID {$userId}, 路径: {$path}");
                
                return json([
                    'error_code' => 403,
                    'msg' => '您已被禁言，无法进行此操作。如有疑问请联系管理员。',
                    'data' => [
                        'banned' => true,
                        'user_status' => '禁言'
                    ]
                ]);
            }

            // 用户状态正常，继续处理请求
            return $next($request);

        } catch (\Exception $e) {
            Log::error('禁言检查中间件异常: ' . $e->getMessage());
            // 发生异常时放行，由其他中间件处理
            return $next($request);
        }
    }

    /**
     * 检查路径是否在白名单中
     * @param string $path 请求路径
     * @return bool
     */
    private function isInWhitelist(string $path): bool
    {
        // 标准化路径（移除开头的斜杠）
        $path = '/' . ltrim($path, '/');
        
        foreach ($this->whitelist as $whitelistPath) {
            // 精确匹配
            if ($path === $whitelistPath) {
                return true;
            }
            
            // 前缀匹配（用于模块路径）
            if (strpos($path, $whitelistPath) === 0) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 添加白名单路径
     * @param string|array $paths 路径或路径数组
     */
    public function addWhitelist($paths): void
    {
        if (is_string($paths)) {
            $paths = [$paths];
        }
        
        $this->whitelist = array_merge($this->whitelist, $paths);
    }

    /**
     * 移除白名单路径
     * @param string $path 路径
     */
    public function removeWhitelist(string $path): void
    {
        $key = array_search($path, $this->whitelist);
        if ($key !== false) {
            unset($this->whitelist[$key]);
        }
    }

    /**
     * 获取当前白名单
     * @return array
     */
    public function getWhitelist(): array
    {
        return $this->whitelist;
    }
}
