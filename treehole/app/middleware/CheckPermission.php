<?php
declare(strict_types=1);

namespace app\middleware;

use app\service\PermissionService;
use think\Request;
use think\Response;
use think\facade\Log;

/**
 * 权限检查中间件
 * 统一处理各种权限验证
 */
class CheckPermission
{
    protected $permissionService;

    public function __construct()
    {
        $this->permissionService = new PermissionService();
    }

    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @param string|null $permission 权限类型
     * @return Response
     */
    public function handle(Request $request, \Closure $next, ?string $permission = null)
    {
        // 获取用户ID（由CheckToken中间件设置）
        $userId = $request->middleware('userId');
        
        if (!$userId) {
            Log::warning('权限检查 - 用户ID不存在');
            return json([
                'code' => 401,
                'msg' => '用户未登录'
            ]);
        }

        // 如果没有指定权限要求，直接通过
        if (!$permission) {
            return $next($request);
        }

        // 解析权限参数
        $permissionConfig = $this->parsePermission($permission);
        
        // 执行权限检查
        $hasPermission = $this->checkPermission($userId, $permissionConfig, $request);
        
        if (!$hasPermission) {
            Log::warning("权限检查失败 - 用户ID: {$userId}, 权限: {$permission}");
            return json([
                'code' => 403,
                'msg' => $this->getPermissionErrorMessage($permissionConfig)
            ]);
        }

        Log::info("权限检查通过 - 用户ID: {$userId}, 权限: {$permission}");
        return $next($request);
    }

    /**
     * 解析权限参数
     * @param string $permission 权限字符串
     * @return array
     */
    private function parsePermission(string $permission): array
    {
        // 支持的权限格式：
        // base:verified - 基础权限
        // role:group_admin:group:123 - 角色权限
        // manage:group:123 - 管理权限
        // admin - 管理员权限
        
        $parts = explode(':', $permission);
        $type = $parts[0];
        
        switch ($type) {
            case 'base':
                return [
                    'type' => 'base',
                    'level' => $parts[1] ?? 'verified'
                ];
                
            case 'role':
                return [
                    'type' => 'role',
                    'role_type' => $parts[1] ?? '',
                    'target_type' => $parts[2] ?? null,
                    'target_id_param' => $parts[3] ?? null // 参数名或固定值
                ];
                
            case 'manage':
                return [
                    'type' => 'manage',
                    'target_type' => $parts[1] ?? '',
                    'target_id_param' => $parts[2] ?? null
                ];
                
            case 'admin':
                return [
                    'type' => 'base',
                    'level' => '管理员'
                ];
                
            case 'super_admin':
                return [
                    'type' => 'base',
                    'level' => '超级管理员'
                ];
                
            default:
                return [
                    'type' => 'base',
                    'level' => 'verified'
                ];
        }
    }

    /**
     * 执行权限检查
     * @param int $userId 用户ID
     * @param array $config 权限配置
     * @param Request $request 请求对象
     * @return bool
     */
    private function checkPermission(int $userId, array $config, Request $request): bool
    {
        switch ($config['type']) {
            case 'base':
                return $this->permissionService->hasBasePermission($userId, $config['level']);
                
            case 'role':
                $targetId = $this->getTargetId($config['target_id_param'], $request);
                return $this->permissionService->hasRole(
                    $userId,
                    $config['role_type'],
                    $targetId,
                    $config['target_type']
                );
                
            case 'manage':
                $targetId = $this->getTargetId($config['target_id_param'], $request);
                if (!$targetId) {
                    return false;
                }
                
                switch ($config['target_type']) {
                    case 'group':
                        return $this->permissionService->canManageGroup($userId, $targetId);
                    case 'organization':
                        return $this->permissionService->canManageOrganization($userId, $targetId);
                    default:
                        return false;
                }
                
            default:
                return false;
        }
    }

    /**
     * 获取目标ID
     * @param string|null $param 参数名或固定值
     * @param Request $request 请求对象
     * @return int|null
     */
    private function getTargetId(?string $param, Request $request): ?int
    {
        if (!$param) {
            return null;
        }
        
        // 如果是数字，直接返回
        if (is_numeric($param)) {
            return (int)$param;
        }
        
        // 从请求参数中获取
        $value = $request->param($param);
        return $value ? (int)$value : null;
    }

    /**
     * 获取权限错误消息
     * @param array $config 权限配置
     * @return string
     */
    private function getPermissionErrorMessage(array $config): string
    {
        switch ($config['type']) {
            case 'base':
                switch ($config['level']) {
                    case 'verified':
                        return '需要完成学生认证';
                    case '管理员':
                        return '需要管理员权限';
                    case '超级管理员':
                        return '需要超级管理员权限';
                    default:
                        return '权限不足';
                }
                
            case 'role':
                $roleNames = [
                    'group_owner' => '群主',
                    'group_admin' => '群管理员',
                    'official_account_admin' => '公众号管理员',
                    'club_admin' => '社团管理员',
                    'business_admin' => '商家管理员',
                    'institution_admin' => '商业机构管理员'
                ];
                $roleName = $roleNames[$config['role_type']] ?? '特定角色';
                return "需要{$roleName}权限";
                
            case 'manage':
                $targetNames = [
                    'group' => '群聊',
                    'organization' => '组织'
                ];
                $targetName = $targetNames[$config['target_type']] ?? '资源';
                return "没有{$targetName}管理权限";
                
            default:
                return '权限不足';
        }
    }
}

/**
 * 权限检查助手函数
 */
class PermissionHelper
{
    /**
     * 快速检查基础权限
     * @param int $userId 用户ID
     * @param string $level 权限级别
     * @return bool
     */
    public static function hasBase(int $userId, string $level = 'verified'): bool
    {
        $service = new PermissionService();
        return $service->hasBasePermission($userId, $level);
    }

    /**
     * 快速检查角色权限
     * @param int $userId 用户ID
     * @param string $roleType 角色类型
     * @param int|null $targetId 目标ID
     * @param string|null $targetType 目标类型
     * @return bool
     */
    public static function hasRole(int $userId, string $roleType, ?int $targetId = null, ?string $targetType = null): bool
    {
        $service = new PermissionService();
        return $service->hasRole($userId, $roleType, $targetId, $targetType);
    }

    /**
     * 快速检查管理权限
     * @param int $userId 用户ID
     * @param string $type 类型（group/organization）
     * @param int $targetId 目标ID
     * @return bool
     */
    public static function canManage(int $userId, string $type, int $targetId): bool
    {
        $service = new PermissionService();
        
        switch ($type) {
            case 'group':
                return $service->canManageGroup($userId, $targetId);
            case 'organization':
                return $service->canManageOrganization($userId, $targetId);
            default:
                return false;
        }
    }
}
