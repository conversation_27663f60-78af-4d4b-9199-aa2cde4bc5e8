# 🏗️ 树洞项目目录结构

## 📋 项目概述
大学城树洞微信小程序项目，前端使用微信开发者工具，后端使用ThinkPHP8，数据库使用MySQL，服务器是Apache。

## 📁 完整目录结构

```
treehole/                           # 项目根目录
├── PROJECT_STRUCTURE.md            # 本文档 - 项目结构说明
├── think                           # ThinkPHP命令行工具
├── composer.json                   # Composer依赖配置
├── composer.lock                   # Composer锁定文件
│
├── app/                            # 应用目录
│   ├── controller/                 # 控制器目录
│   │   ├── Index.php              # 首页控制器
│   │   ├── User.php               # 用户控制器
│   │   ├── Message.php            # 消息控制器
│   │   ├── Email.php              # 邮件控制器
│   │   └── ...                    # 其他业务控制器
│   │
│   ├── middleware/                 # 中间件目录
│   │   ├── FilterInjection.php    # SQL注入过滤中间件
│   │   └── SecurityMonitor.php    # 安全监控中间件
│   │
│   ├── service/                    # 服务层目录
│   │   ├── AlertService.php       # 告警服务
│   │   ├── CacheService.php       # 智能缓存服务
│   │   ├── DatabaseAdapter.php    # 数据库适配器
│   │   └── WeeklyReportService.php # 周报生成服务
│   │
│   ├── command/                    # 命令行工具目录
│   │   ├── SecurityScan.php       # 安全扫描命令
│   │   ├── SecurityClean.php      # 安全清理命令
│   │   ├── CacheMonitor.php       # 缓存监控命令
│   │   └── WeeklyReport.php       # 周报生成命令
│   │
│   ├── validate/                   # 验证器目录
│   │   └── UserValidate.php       # 用户验证器
│   │
│   ├── model/                      # 模型目录
│   │   ├── User.php               # 用户模型
│   │   ├── Message.php            # 消息模型
│   │   └── ...                    # 其他模型
│   │
│   ├── BaseController.php          # 基础控制器
│   ├── ExceptionHandle.php         # 异常处理
│   ├── Request.php                 # 请求类
│   └── middleware.php              # 中间件配置
│
├── config/                         # 配置目录
│   ├── app.php                    # 应用配置
│   ├── database.php               # 数据库配置
│   ├── cache.php                  # 缓存配置
│   ├── secrets.php                # 密钥配置（包含安全监控配置）
│   └── ...                        # 其他配置文件
│
├── public/                         # 公共目录（Web根目录）
│   ├── index.php                  # 入口文件
│   ├── .htaccess                  # Apache重写规则
│   ├── PHPMailer/                 # PHPMailer邮件库
│   │   ├── src/
│   │   │   ├── PHPMailer.php
│   │   │   ├── SMTP.php
│   │   │   └── Exception.php
│   │   └── ...
│   ├── static/                    # 静态资源
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── uploads/                   # 上传文件目录
│
├── vendor/                         # Composer依赖目录
│   ├── topthink/                  # ThinkPHP框架
│   └── ...                        # 其他第三方库
│
├── runtime/                        # 运行时目录
│   ├── cache/                     # 缓存文件
│   ├── log/                       # 框架日志
│   └── temp/                      # 临时文件
│
├── logs/                          # 项目日志目录
│   ├── php_error.log              # PHP错误日志
│   ├── security_monitor.log       # 安全监控日志
│   ├── security_scan.log          # 安全扫描日志
│   ├── security_scan_YYYY-MM-DD.json  # 每日扫描报告
│   ├── weekly_report_YYYY-MM-DD.json  # 每周安全报告
│   ├── system_status_YYYYMMDD.json     # 系统状态报告
│   └── cron.log                   # 定时任务日志
│
├── scripts/                       # 运维脚本目录
│   ├── README.md                  # 脚本说明文档
│   ├── security_monitor.sh        # 安全监控脚本
│   ├── check_environment.sh        # 环境配置检查脚本
│   └── crontab.example           # 定时任务配置模板
│
├── docs/                          # 文档目录
│   ├── SECURITY_MONITORING.md     # 安全监控文档
│   └── API.md                     # API文档（如果有）
│
├── mysql/                         # 数据库相关
│   ├── create_tables.sql          # 建表SQL
│   ├── init_data.sql              # 初始化数据
│   └── updates/                   # 数据库更新脚本
│       ├── 2024-01-01_add_security_fields.sql
│       └── ...
│
└── .gitignore                     # Git忽略文件
```

## 🔧 核心功能模块

### 1. 安全监控系统
- **中间件**：`app/middleware/SecurityMonitor.php` - 实时监控异常请求
- **过滤器**：`app/middleware/FilterInjection.php` - SQL注入防护
- **扫描工具**：`app/command/SecurityScan.php` - 定期安全扫描
- **清理工具**：`app/command/SecurityClean.php` - 恶意数据清理
- **告警服务**：`app/service/AlertService.php` - 邮件告警通知

### 2. 用户认证系统
- **邮件验证**：`app/controller/Email.php` - 学生邮箱认证
- **用户验证**：`app/validate/UserValidate.php` - 用户名安全验证
- **用户模型**：`app/model/User.php` - 用户数据管理

### 3. 消息系统
- **消息控制器**：`app/controller/Message.php` - 树洞消息管理
- **消息模型**：`app/model/Message.php` - 消息数据处理

## ⚙️ 配置说明

### 安全配置 (`config/secrets.php`)
```php
'security' => [
    'admin_email' => '<EMAIL>',           // 管理员邮箱
    'alert_enabled' => true,                       // 启用告警
    'scan_frequency' => $isLocal ? 'daily' : 'daily', // 扫描频率
    'max_failed_logins' => $isLocal ? 5 : 10,      // 失败登录阈值
    'max_requests_per_minute' => $isLocal ? 50 : 100, // 请求频率限制
    'email_from' => $isLocal ? '<EMAIL>' : '<EMAIL>',
    'email_from_name' => $isLocal ? '树洞安全监控系统(开发)' : '树洞安全监控系统',
    'log_level' => $isLocal ? 'debug' : 'warning', // 日志级别
],
```

### 中间件配置 (`app/middleware.php`)
```php
return [
    \app\middleware\FilterInjection::class,    // SQL注入过滤
    \app\middleware\SecurityMonitor::class,    // 安全监控
];
```

## 🚀 运维脚本

### 安全监控脚本 (`scripts/security_monitor.sh`)
- **每日扫描**：`./scripts/security_monitor.sh daily`
- **每小时检查**：`./scripts/security_monitor.sh hourly`
- **每周深度扫描**：`./scripts/security_monitor.sh weekly`

### ThinkPHP命令
- **安全扫描**：`php think security:scan`
- **安全清理**：`php think security:clean`

## 📊 日志系统

### 日志文件分类
- **安全日志**：记录攻击尝试和安全事件
- **系统日志**：记录系统运行状态
- **错误日志**：记录PHP错误和异常
- **定时任务日志**：记录定时任务执行情况

### 日志轮转策略
- 安全扫描报告：保留7天
- 错误日志：30天后压缩，90天后删除
- 系统状态报告：每日生成，保留30天

## 🔒 安全特性

### 多层防护
1. **输入验证**：严格的参数验证和过滤
2. **SQL注入防护**：中间件实时检测和阻止
3. **频率限制**：IP请求频率监控和限制
4. **实时监控**：异常行为实时检测和告警
5. **定期扫描**：自动检测和清理恶意数据

### 告警机制
- **实时告警**：高频请求、SQL注入尝试
- **定期报告**：每日安全扫描结果
- **邮件通知**：使用PHPMailer发送告警邮件

## 📞 维护说明

### 定时任务设置
```bash
# 复制配置模板
cp scripts/crontab.example /tmp/treehole_crontab

# 替换路径
sed -i 's|/path/to/treehole|'$(pwd)'|g' /tmp/treehole_crontab

# 安装定时任务
crontab /tmp/treehole_crontab
```

### 常用维护命令
```bash
# 环境配置检查
./scripts/check_environment.sh

# 查看安全日志
tail -f logs/security_monitor.log

# 手动执行安全扫描
php think security:scan

# 手动清理恶意数据
php think security:clean

# 查看系统状态
cat logs/system_status_$(date +%Y%m%d).json
```

## 🎯 开发规范

### 代码结构
- **Controller**：只负责接收请求、验证参数、调用Service层
- **Service**：处理核心业务逻辑
- **Model**：只负责数据表的直接交互
- **Middleware**：统一处理横切关注点（安全、日志等）

### 安全要求
- 所有外部输入必须经过验证
- 使用框架的查询构造器/ORM模型
- 从请求头获取token进行身份验证
- 重要数据使用Redis缓存
- 图片等静态资源使用COS存储

---

**注意**：此文档会随着项目发展持续更新，请定期查看最新版本。
