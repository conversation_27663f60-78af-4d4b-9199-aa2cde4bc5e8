{"scan_time": "2025-08-12 22:17:16", "malicious_users_count": 0, "suspicious_posts_count": 0, "abnormal_requests_count": 0, "failed_logins_count": 0, "details": {"malicious_users": [], "suspicious_posts": [], "abnormal_requests": [], "failed_logins": [], "cache_stats": {"redis_available": true, "redis_failure_count": 0, "last_check_time": "2025-08-12 22:17:16", "cache_type": "Redis", "file_cache_count": 0}, "db_structure": {"scan_time": "2025-08-12 22:17:16", "tables": {"user": {"exists": true, "fields": ["status", "activity_subscribed", "last_view_activity_time", "<PERSON><PERSON><PERSON>", "id", "openid", "username", "phone", "password", "face_url", "subject_image", "unread", "code", "code_generated_at", "titlename", "titlecolor", "schoolname", "school_id", "verified_university_id", "last_request_time", "request_count", "status_code"], "time_field": null, "user_id_field": "user_id", "content_field": "content", "basic_fields": ["id", "username", "openid"]}, "message": {"exists": true, "fields": ["choose", "id", "user_id", "username", "face_url", "ispull", "content", "total_likes", "send_timestamp", "images", "jine", "we<PERSON>hi", "titlename", "titlecolor", "vx", "qq", "phonenumber", "scheme", "has_vote", "vote_id", "views", "is_anonymous", "flag"], "time_field": "send_timestamp", "user_id_field": "user_id", "content_field": "content", "basic_fields": ["id", "username", "user_id", "content", "send_timestamp"]}, "comment": {"exists": true, "fields": ["id", "user_id", "reply_to_user_id", "reply_to_username", "username", "face_url", "content", "images", "total_likes", "total_pinglun", "send_timestamp", "message_id", "titlename", "titlecolor", "anonymous_name", "anonymous_avatar", "is_from_anonymous_post", "is_deleted"], "time_field": "send_timestamp", "user_id_field": "user_id", "content_field": "content", "basic_fields": ["id", "username", "user_id", "content", "send_timestamp"]}, "post": {"exists": true, "fields": ["id", "user_id", "reply_to_user_id", "reply_to_username", "reply_to_post_id", "username", "face_url", "content", "images", "total_likes", "total_pinglun", "send_timestamp", "message_id", "comment_id", "parent_comment_id", "reply_type", "titlename", "titlecolor", "anonymous_name", "anonymous_avatar", "is_from_anonymous_post", "is_deleted"], "time_field": "send_timestamp", "user_id_field": "user_id", "content_field": "content", "basic_fields": ["id", "username", "user_id", "content", "send_timestamp"]}, "notification": {"exists": true, "fields": ["id", "user_id", "from_user_id", "type", "target_type", "target_id", "message_id", "content", "target_content", "content_image", "is_read", "is_anonymous", "anonymous_name", "created_at"], "time_field": "created_at", "user_id_field": "user_id", "content_field": "content", "basic_fields": ["id", "user_id", "content", "created_at"]}}}}}