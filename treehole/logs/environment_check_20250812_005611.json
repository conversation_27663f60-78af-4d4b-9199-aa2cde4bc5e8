{"check_time": "2025-08-11 16:56:11", "php_version": "8.3.14", "php_sapi": "cli", "environment": {"type": "development", "database_host": "localhost", "redis_host": "127.0.0.1", "security_config": {"admin_email": "<EMAIL>", "alert_enabled": true, "scan_frequency": "daily", "max_failed_logins": 5, "max_requests_per_minute": 50, "email_from": "<EMAIL>", "email_from_name": "树洞安全监控系统(开发)", "log_level": "debug"}}, "middleware": ["app\\middleware\\FilterInjection", "app\\middleware\\SecurityMonitor"], "commands": {"draft:generate": "app\\command\\DraftGenerate", "send": "app\\command\\Send", "tas": "\\app\\command\\Tas", "draft-task": "app\\command\\DraftTask", "log:cleanup": "app\\command\\LogCleanup", "security:scan": "app\\command\\SecurityScan", "security:clean": "app\\command\\SecurityClean"}, "extensions": {"redis": true}}