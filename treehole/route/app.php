<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 根路径路由 - 显示 home.php 内容
Route::get('/', function () {
    return file_get_contents(__DIR__ . '/../public/home.php');
});

Route::get('think', function () {
    return 'hello,ThinkPHP6!';
});

Route::get('hello/:name', 'index/hello');

// 用户角色管理路由组
Route::group('userRole', function () {
    // 获取用户角色列表
    Route::post('getUserRoles', 'UserRole/getUserRoles');

    // 授予用户角色 - 需要管理权限
    Route::post('grantRole', 'UserRole/grantRole');

    // 撤销用户角色 - 需要管理权限
    Route::post('revokeRole', 'UserRole/revokeRole');

    // 获取角色类型列表
    Route::post('getRoleTypes', 'UserRole/getRoleTypes');

    // 批量检查权限
    Route::post('checkPermissions', 'UserRole/checkPermissions');
});

// UID管理路由
Route::post('auth/batchAssignUids', 'Auth/batchAssignUids');


