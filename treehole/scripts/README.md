# 🛠️ 脚本目录说明

这个目录包含了树洞项目的Windows环境脚本文件。

## 📁 目录结构

```
scripts/
├── README.md                    # 本说明文件
├── install_windows_tasks.ps1    # Windows定时任务安装脚本
└── manage_windows_tasks.ps1     # Windows定时任务管理脚本
```

## 🔧 脚本说明

### install_windows_tasks.ps1
**功能**：自动安装Windows定时任务
**用法**：
```powershell
# 以管理员身份运行PowerShell
# 进入项目目录
cd C:\path\to\your\treehole

# 安装定时任务
.\scripts\install_windows_tasks.ps1
```

### manage_windows_tasks.ps1
**功能**：管理Windows定时任务
**用法**：
```powershell
# 查看任务列表
.\scripts\manage_windows_tasks.ps1 -Action list

# 查看任务状态
.\scripts\manage_windows_tasks.ps1 -Action status

# 启动所有任务
.\scripts\manage_windows_tasks.ps1 -Action start

# 停止指定任务
.\scripts\manage_windows_tasks.ps1 -Action stop -TaskName SecurityScan

# 手动测试任务
.\scripts\manage_windows_tasks.ps1 -Action test -TaskName CacheMonitor
```

## 🚀 快速开始

### 1. 安装定时任务
```powershell
# 以管理员身份运行PowerShell
cd C:\path\to\your\treehole
.\scripts\install_windows_tasks.ps1
```

### 2. 验证安装
```powershell
# 查看任务列表
.\scripts\manage_windows_tasks.ps1 -Action list
```

### 3. 测试任务
```powershell
# 手动测试安全扫描
.\scripts\manage_windows_tasks.ps1 -Action test -TaskName SecurityScan
```

## 📊 定时任务列表

| 任务名称 | 描述 | 执行频率 | 执行时间 |
|---------|------|---------|---------|
| TreeHole-SecurityScan | 安全扫描 | 每天 | 凌晨2:00 |
| TreeHole-CacheMonitor | 缓存监控 | 每30分钟 | 全天 |
| TreeHole-WeeklyReport | 周报生成 | 每周一 | 上午9:00 |
| TreeHole-EventCleanup | 事件清理 | 每天 | 凌晨6:00 |

## ⚠️ 注意事项

1. **权限要求**：需要以管理员身份运行PowerShell
2. **PHP环境**：确保PHP在系统PATH中
3. **自动恢复**：系统重启后任务会自动恢复
4. **本地开发**：本地环境可选择不安装定时任务

## 📝 详细文档

更多详细信息请参考：`docs/WINDOWS_TASKS_GUIDE.md`

## 🔍 故障排除

### 常见问题

1. **权限不足**
   - 以管理员身份运行PowerShell

2. **PHP命令找不到**
   ```powershell
   # 检查PHP是否可用
   php -v
   ```

3. **任务不执行**
   - 确保计算机在执行时间开机
   - 检查任务计划程序中的任务状态

### 查看执行日志
```powershell
# 查看Windows事件日志
Get-WinEvent -LogName "Microsoft-Windows-TaskScheduler/Operational" | Where-Object {$_.Message -like "*TreeHole*"}
```
