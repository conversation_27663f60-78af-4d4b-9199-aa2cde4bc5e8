# Simple Windows Tasks Installation Script
# Run as Administrator

Write-Host "Installing TreeHole Windows Tasks..." -ForegroundColor Green

# Get project path
$ProjectPath = (Get-Location).Path
Write-Host "Project Path: $ProjectPath" -ForegroundColor Yellow

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "ERROR: Please run as Administrator" -ForegroundColor Red
    exit 1
}

# Task 1: Security Scan
Write-Host "Creating Security Scan Task..." -ForegroundColor Cyan
$action1 = New-ScheduledTaskAction -Execute "php" -Argument "think security:scan" -WorkingDirectory $ProjectPath
$trigger1 = New-ScheduledTaskTrigger -Daily -At "02:00"
$settings1 = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "TreeHole-SecurityScan" -Action $action1 -Trigger $trigger1 -Settings $settings1 -Description "TreeHole Security Scan" -Force

# Task 2: Cache Monitor
Write-Host "Creating Cache Monitor Task..." -ForegroundColor Cyan
$action2 = New-ScheduledTaskAction -Execute "php" -Argument "think cache:monitor" -WorkingDirectory $ProjectPath
$trigger2 = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 30) -RepetitionDuration (New-TimeSpan -Days 365)
$settings2 = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "TreeHole-CacheMonitor" -Action $action2 -Trigger $trigger2 -Settings $settings2 -Description "TreeHole Cache Monitor" -Force

# Task 3: Weekly Report
Write-Host "Creating Weekly Report Task..." -ForegroundColor Cyan
$action3 = New-ScheduledTaskAction -Execute "php" -Argument "think report:weekly" -WorkingDirectory $ProjectPath
$trigger3 = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At "09:00"
$settings3 = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "TreeHole-WeeklyReport" -Action $action3 -Trigger $trigger3 -Settings $settings3 -Description "TreeHole Weekly Report" -Force

# Task 4: Event Cleanup
Write-Host "Creating Event Cleanup Task..." -ForegroundColor Cyan
$action4 = New-ScheduledTaskAction -Execute "php" -Argument "think security:monitor --clean" -WorkingDirectory $ProjectPath
$trigger4 = New-ScheduledTaskTrigger -Daily -At "06:00"
$settings4 = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "TreeHole-EventCleanup" -Action $action4 -Trigger $trigger4 -Settings $settings4 -Description "TreeHole Event Cleanup" -Force

Write-Host "All tasks created successfully!" -ForegroundColor Green
Write-Host "You can view tasks in Task Scheduler or run:" -ForegroundColor Yellow
Write-Host "Get-ScheduledTask | Where-Object {`$_.TaskName -like 'TreeHole-*'}" -ForegroundColor Cyan
