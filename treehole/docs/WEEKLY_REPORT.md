# 📊 安全监控周报系统

## 📋 概述

树洞系统的安全监控周报会每周自动生成并发送到管理员邮箱，提供全面的系统安全状况分析。

## 📧 周报内容

### 1. 📊 安全摘要
- **安全扫描次数**：本周执行的安全扫描总数
- **发现恶意用户名**：检测到的恶意用户名数量
- **可疑帖子**：发现的可疑内容数量
- **异常请求**：检测到的异常请求次数
- **安全等级**：基于威胁数量的整体安全评级
  - 🟢 安全：无威胁或威胁极少
  - 🟡 注意：少量威胁，需要关注
  - 🟠 警告：威胁较多，需要处理
  - 🔴 高危：威胁严重，需要立即处理

### 2. 👥 用户活动统计
- **总用户数**：系统注册用户总数
- **总消息数**：系统消息总数
- **本周新消息**：本周新增的消息数量

### 3. ⚡ 系统性能
- **PHP版本**：当前运行的PHP版本
- **内存使用**：当前内存使用情况和峰值
- **磁盘使用率**：系统磁盘空间使用百分比

### 4. 💾 缓存系统
- **Redis状态**：Redis服务的可用性状态
- **当前缓存类型**：正在使用的缓存类型（Redis/文件缓存）
- **文件缓存数量**：备用文件缓存的数量
- **本周故障统计**：Redis服务故障次数和恢复情况

### 5. 🚨 威胁分析
- **SQL注入尝试**：检测到的SQL注入攻击次数
- **高频请求告警**：触发高频请求告警的次数
- **可疑User-Agent**：检测到的可疑用户代理数量

### 6. 💡 建议和改进
基于系统状态自动生成的优化建议：
- Redis服务状态建议
- 磁盘空间管理建议
- 日志文件清理建议
- 系统性能优化建议

## ⏰ 发送时间

- **发送频率**：每周一次
- **发送时间**：每周一上午9:00
- **收件人**：<EMAIL>

## 🛠️ 手动生成周报

### 命令行生成
```bash
# 手动生成并发送周报
php think report:weekly
```

### 脚本生成
```bash
# 使用监控脚本生成（包含完整的周度检查）
./scripts/security_monitor.sh weekly
```

## 📁 报告文件

### 邮件报告
- **格式**：纯文本邮件
- **内容**：包含所有关键指标的摘要
- **主题**：🛡️ 树洞系统安全监控周报 - YYYY年MM月DD日

### 详细数据文件
- **位置**：`logs/weekly_report_YYYY-MM-DD.json`
- **格式**：JSON格式，包含完整的原始数据
- **用途**：详细分析和历史对比

## 📈 数据统计周期

- **统计周期**：过去7天（从报告生成时间往前推算）
- **数据来源**：
  - 安全扫描日志文件
  - 系统错误日志
  - 数据库统计信息
  - 缓存系统状态
  - 系统性能指标

## 🔧 配置和自定义

### 修改收件人
编辑 `config/secrets.php`：
```php
'security' => [
    'admin_email' => '<EMAIL>', // 修改为你的邮箱
],
```

### 修改发送时间
编辑定时任务配置：
```bash
# 编辑 scripts/crontab.example 或直接修改 crontab
# 当前配置：每周一上午9点
0 9 * * 1 cd /path/to/treehole && php think report:weekly

# 修改为其他时间，例如每周五下午5点：
0 17 * * 5 cd /path/to/treehole && php think report:weekly
```

### 自定义报告内容
修改 `app/service/WeeklyReportService.php` 中的相关方法：
- `collectWeeklyData()` - 修改数据收集逻辑
- `formatReportContent()` - 修改邮件内容格式
- `getRecommendations()` - 修改建议生成逻辑

## 📊 报告示例

```
# 🛡️ 树洞系统安全监控周报

**报告周期：** 2025-08-05 至 2025-08-12
**生成时间：** 2025-08-12 09:00:00

## 📊 安全摘要

- **安全扫描次数：** 7 次
- **发现恶意用户名：** 0 个
- **可疑帖子：** 0 个
- **异常请求：** 0 次
- **安全等级：** 🟢 安全

## 👥 用户活动统计

- **总用户数：** 8072 人
- **总消息数：** 2277 条
- **本周新消息：** 31 条

## ⚡ 系统性能

- **PHP版本：** 8.3.14
- **内存使用：** 6MB (峰值: 6MB)
- **磁盘使用率：** 85.3%

## 💾 缓存系统

- **Redis状态：** ✅ 正常
- **当前缓存类型：** Redis
- **文件缓存数量：** 1 个

## 🚨 威胁分析

- **SQL注入尝试：** 0 次
- **高频请求告警：** 0 次
- **可疑User-Agent：** 0 次

## 💡 建议和改进

- 💾 磁盘使用率较高(85.3%)，建议清理日志文件或扩容
- ✅ 系统运行良好，暂无特别建议

---
*此报告由树洞安全监控系统自动生成*
*如有问题，请联系系统管理员*
```

## 🔍 故障排除

### 周报未收到
1. **检查邮箱配置**：确认 `config/secrets.php` 中的邮箱地址正确
2. **检查定时任务**：确认 crontab 中的周报任务已正确配置
3. **手动测试**：运行 `php think report:weekly` 测试生成功能
4. **查看日志**：检查 `logs/cron.log` 和 `logs/php_error.log`

### 报告内容异常
1. **数据库连接**：确认数据库连接正常
2. **权限问题**：确认日志目录有写入权限
3. **缓存问题**：检查Redis服务状态
4. **手动调试**：使用 `php think report:weekly` 查看详细错误信息

### 邮件发送失败
1. **SMTP配置**：检查 `app/service/AlertService.php` 中的SMTP设置
2. **网络连接**：确认服务器可以访问邮件服务器
3. **邮箱授权**：确认发件邮箱的授权码正确
4. **防火墙**：检查防火墙是否阻止了SMTP端口

## 📞 技术支持

如果遇到问题：
1. 查看相关日志文件
2. 运行环境检查脚本：`./scripts/check_environment.sh`
3. 手动测试各个组件功能
4. 联系技术支持团队

---

**注意**：周报系统会自动适应数据库结构变更，如果数据库表结构发生变化，系统会自动调整并在报告中说明。
