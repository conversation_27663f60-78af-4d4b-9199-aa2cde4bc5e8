# 🚀 Windows环境部署检查清单

## 📋 Windows环境首次部署清单

### 1. 代码部署
- [ ] 上传所有项目文件到服务器
- [ ] 确保文件权限正确（755 for directories, 644 for files）
- [ ] 给脚本文件执行权限：`chmod +x scripts/*.sh`

### 2. 环境检查
```bash
# 检查PHP版本
php -v

# 检查ThinkPHP命令
php think list

# 检查必要的PHP扩展
php -m | grep -E "(redis|mysqli|curl)"
```

### 3. 配置文件
- [ ] 配置数据库连接
- [ ] 配置邮件SMTP设置
- [ ] 配置Redis连接（如果使用）
- [ ] 设置正确的时区

### 4. 安装定时任务
```powershell
# 以管理员身份运行PowerShell
# 进入项目目录
cd C:\path\to\your\treehole

# 安装定时任务
.\scripts\install_windows_tasks.ps1

# 验证安装
.\scripts\manage_windows_tasks.ps1 -Action list
```

### 5. 测试功能
```powershell
# 测试安全扫描
.\scripts\manage_windows_tasks.ps1 -Action test -TaskName SecurityScan

# 测试缓存监控
.\scripts\manage_windows_tasks.ps1 -Action test -TaskName CacheMonitor

# 测试邮件发送
php think security:monitor
# 选择 "7. 发送测试告警"
```

### 6. 创建日志目录
```bash
mkdir -p logs
touch logs/cron.log logs/php_error.log
chmod 666 logs/*.log
```

## 🔄 代码更新清单

### 何时需要重新安装定时任务？
- ❌ **不需要**：修改PHP代码、配置文件、业务逻辑
- ✅ **需要**：修改定时任务时间、添加新任务、更改项目路径

### 常规代码更新步骤
1. **备份重要文件**
   ```bash
   cp config/database.php config/database.php.bak
   cp config/secrets.php config/secrets.php.bak
   ```

2. **上传新代码**
   - 上传修改的文件
   - 保持目录结构不变

3. **验证更新**
   ```bash
   # 测试关键功能
   php think security:scan
   php think cache:monitor
   
   # 查看日志确认无错误
   tail -f logs/php_error.log
   ```

## 🔍 监控和维护

### 每日检查
```bash
# 查看定时任务执行日志
tail -20 logs/cron.log

# 查看系统错误日志
tail -20 logs/php_error.log

# 查看今日安全事件
php think security:monitor
# 选择 "2. 查看今日安全事件"
```

### 每周检查
```bash
# 查看安全统计
php think security:monitor
# 选择 "1. 查看安全事件统计"

# 清理过期事件
php think security:monitor --clean

# 检查磁盘空间
df -h
```

### 故障排除
```bash
# 检查cron服务状态
systemctl status crond

# 查看cron系统日志
tail -f /var/log/cron

# 检查PHP错误
tail -f logs/php_error.log

# 手动执行定时任务测试
./scripts/security_monitor.sh daily
```

## 🚨 紧急情况处理

### 如果定时任务停止工作
1. **检查cron服务**
   ```bash
   systemctl status crond
   systemctl restart crond
   ```

2. **检查定时任务配置**
   ```bash
   crontab -l
   ```

3. **重新安装定时任务**
   ```bash
   ./scripts/install_crontab.sh
   ```

### 如果收到大量告警邮件
1. **查看安全事件**
   ```bash
   php think security:monitor
   ```

2. **临时禁用告警**（紧急情况）
   ```bash
   # 备份当前定时任务
   crontab -l > crontab_backup.txt
   
   # 临时清空定时任务
   crontab -r
   ```

3. **分析问题后恢复**
   ```bash
   crontab crontab_backup.txt
   ```

## 📊 性能监控

### 系统资源监控
```bash
# CPU使用率
top

# 内存使用
free -h

# 磁盘使用
df -h

# 网络连接
netstat -an | grep :80
```

### 应用性能监控
```bash
# PHP进程
ps aux | grep php

# 数据库连接
mysql -e "SHOW PROCESSLIST;"

# Redis状态（如果使用）
redis-cli info
```

## 📝 备份策略

### 定期备份
```bash
# 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/

# 备份日志文件
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/
```

### 自动备份（可选）
```bash
# 添加到定时任务
0 1 * * 0 /path/to/backup_script.sh
```

---

## 🎯 总结

### 首次部署：需要安装定时任务
### 代码更新：通常不需要重新安装
### 服务器重启：定时任务自动恢复
### 监控方式：查看日志和邮件告警

**记住**：定时任务是系统级的，一旦安装就会持续运行，除非手动删除或系统故障。
