# Token 验证修复文档

## 问题描述

前端在调用 `getMessages` 等接口时出现 token 验证失败的问题，原因是：

1. **前端使用了多种 token 传递方式**
2. **后端接口只检查单一的 token 传递方式**
3. **不同页面的 token 传递方式不统一**

## 前端 Token 传递方式分析

### 发现的不同方式

1. **方式1**: `'token': wx.getStorageSync('access_token')`
   ```javascript
   header: {
     'content-type': 'application/x-www-form-urlencoded',
     'token': wx.getStorageSync('access_token')
   }
   ```

2. **方式2**: `'Authorization': 'Bearer ' + token`
   ```javascript
   header: {
     'Authorization': `Bearer ${token}`
   }
   ```

3. **方式3**: 参数传递
   ```javascript
   data: {
     token: wx.getStorageSync('token'),
     access_token: wx.getStorageSync('access_token')
   }
   ```

### 使用情况统计

- **home.js**: 使用 `'token': wx.getStorageSync('access_token')`
- **hot.js**: 使用 `'token': wx.getStorageSync('access_token')`
- **request.js**: 使用 `'Authorization': 'Bearer ' + token`
- **tokenManager.js**: 使用 `header.token = accessToken`
- **api.js**: 通过 tokenManager 自动处理

## 后端修复方案

### 1. 创建统一的 Token 验证助手

在 `PermissionHelper.php` 中添加了两个新方法：

```php
/**
 * 验证请求中的token并返回用户数据
 * 支持多种token传递方式
 */
public static function validateRequestToken(Request $request): ?array
{
    // 从多种方式获取token
    $token = $request->header('Authorization', '');
    if (empty($token)) {
        $token = $request->header('token', '');
    }
    if (empty($token)) {
        $token = $request->param('token', '');
        $token = $request->param('access_token', $token);
    }
    
    // 处理带有Bearer前缀的token
    if (is_string($token) && strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    return JwtUtil::validateToken($token);
}

/**
 * 验证token并返回标准的JSON响应
 */
public static function validateTokenWithResponse(Request $request): array
{
    $userData = self::validateRequestToken($request);
    
    if (!$userData) {
        return [
            'success' => false,
            'response' => ['error_code' => 1, 'msg' => '请先登录']
        ];
    }
    
    return [
        'success' => true,
        'user_id' => $userData['user_id'],
        'response' => null
    ];
}
```

### 2. 支持的 Token 传递方式

后端现在支持以下所有方式：

1. **Authorization Header**:
   - `Authorization: Bearer <token>`
   - `Authorization: <token>`

2. **Token Header**:
   - `token: <token>`

3. **请求参数**:
   - `token=<token>`
   - `access_token=<token>`

### 3. 更新的接口

**后端接口**已更新使用新的 token 验证方式：

- ✅ `Message::getMessages()`
- ✅ `Message::searchMessages()`
- ✅ `Message::getOneUserMessage()`
- ✅ `Message::getHotMessages()`
- ✅ `Message::getMessageDetails()`
- ✅ `Message::getVoteDetails()`
- ✅ `Message::softDelete()` - 删除帖子接口
- ✅ `Comment::getComments()`

**前端页面**已更新添加 token 传递：

- ✅ `home.js` - 首页消息加载
- ✅ `hot.js` - 热门消息页面
- ✅ `yijian.js` - 意见反馈页面
- ✅ `messageDetail.js` - 消息详情页面
- ✅ `yijiandetail.js` - 意见详情页面
- ✅ `mypub.js` - 我的发布页面
- ✅ `xuqiu.js` - 需求页面

### 4. 权限检查统一化

**已修复的硬编码权限检查**：

- ✅ `Canteen.php` - 食堂管理权限
- ✅ `Life.php` - 生活服务权限
- ✅ `User.php` - 用户管理权限
- ✅ `Vxgroup.php` - 微信群管理权限
- ✅ `User.php` 模型 - isAdmin() 方法
- ✅ `PermissionHelper.php` - isAdmin() 方法

**统一使用**：
```php
// 旧方式
if ($user['status'] !== '管理员') { ... }

// 新方式
if (!PermissionHelper::hasAdminPermission($userId)) { ... }
```

### 5. 禁言检查统一化

**已移除的手动禁言检查**：

- ✅ `Like.php` - 点赞操作禁言检查
- ✅ `Comment.php` - 评论发布禁言检查
- ✅ `Message.php` - doLike/doLike2 禁言检查

**统一使用全局中间件**：
```php
// 旧方式（已移除）
if ($user['status'] === '禁言') {
    return json(['error_code' => 403, 'msg' => '您已被禁言，无法进行操作']);
}

// 新方式：由 CheckBanned 中间件统一处理
// 控制器中无需手动检查禁言状态
```

**保留的合理禁言检查**：
- ✅ `CheckBanned` 中间件 - 全局禁言拦截
- ✅ 前端 `roleManager.js` - UI 显示判断
- ✅ 权限服务底层 - 权限级别计算

## 使用方法

### 在控制器中使用

```php
public function someAction(Request $request): Json
{
    // 简单的token验证
    $tokenResult = PermissionHelper::validateTokenWithResponse($request);
    if (!$tokenResult['success']) {
        return json($tokenResult['response']);
    }
    
    $userId = $tokenResult['user_id'];
    
    // 继续业务逻辑...
}
```

### 替换原有的验证代码

**原有代码**:
```php
$token = $request->header('token');
if (!$token) {
    return json(['error_code' => 1, 'msg' => '请先登录']);
}

$userData = JwtUtil::validateToken($token);
if (!$userData) {
    return json(['error_code' => 1, 'msg' => 'token无效或已过期']);
}

$userId = $userData['user_id'];
```

**新代码**:
```php
$tokenResult = PermissionHelper::validateTokenWithResponse($request);
if (!$tokenResult['success']) {
    return json($tokenResult['response']);
}

$userId = $tokenResult['user_id'];
```

## 测试方法

### 1. 使用 Apifox 测试

可以使用以下任意一种方式传递 token：

**方式1 - Header token**:
```
Headers:
token: your_access_token_here
```

**方式2 - Authorization Header**:
```
Headers:
Authorization: Bearer your_access_token_here
```

**方式3 - 请求参数**:
```
Body (form-data):
access_token: your_access_token_here
```

### 2. 前端测试

确保前端使用以下任意一种方式：

```javascript
// 方式1 - 推荐
header: {
  'token': wx.getStorageSync('access_token')
}

// 方式2 - 也支持
header: {
  'Authorization': 'Bearer ' + wx.getStorageSync('access_token')
}

// 方式3 - 参数传递
data: {
  access_token: wx.getStorageSync('access_token')
}
```

## 兼容性

### 向后兼容

- ✅ 支持所有现有的 token 传递方式
- ✅ 不需要修改现有的前端代码
- ✅ 与 `CheckToken` 中间件保持一致

### 前端建议

虽然后端支持多种方式，但建议前端统一使用：

```javascript
header: {
  'content-type': 'application/x-www-form-urlencoded',
  'token': wx.getStorageSync('access_token')
}
```

## 错误处理

### 标准错误响应

```json
{
  "error_code": 1,
  "msg": "请先登录"
}
```

### 常见错误

1. **Token 不存在**: `"请先登录"`
2. **Token 无效**: `"token无效或已过期"`
3. **Token 格式错误**: `"token中用户信息无效"`

## 总结

通过这次修复：

1. ✅ **解决了 token 传递不一致的问题**
2. ✅ **提供了统一的 token 验证方法**
3. ✅ **保持了向后兼容性**
4. ✅ **简化了控制器中的验证代码**

现在所有的消息接口都应该能正常工作，无论前端使用哪种 token 传递方式。
