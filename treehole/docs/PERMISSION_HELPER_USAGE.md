# 后端权限助手使用指南

## 概述

`PermissionHelper` 是一个简化的权限管理助手类，类似前端的 `roleManager`，提供了便捷的权限检查方法。

## 基础用法

### 1. 导入权限助手

```php
use app\util\PermissionHelper;
```

### 2. 基础权限检查

```php
// 检查用户是否已认证
$hasBase = PermissionHelper::hasBasePermission($userId);

// 检查用户是否是管理员
$isAdmin = PermissionHelper::hasAdminPermission($userId);

// 检查用户是否是超级管理员
$isSuperAdmin = PermissionHelper::isSuperAdmin($userId);
```

### 3. 功能权限检查

```php
// 检查是否可以发布活动
$canPublish = PermissionHelper::canPublishActivity($userId);

// 检查是否可以管理公众号
$canManageOA = PermissionHelper::canManageOfficialAccounts($userId);

// 检查是否可以管理食堂
$canManageCanteen = PermissionHelper::canManageCanteen($userId);

// 检查是否是社团管理员
$isClubAdmin = PermissionHelper::isClubAdmin($userId);
```

### 4. 附加角色权限检查

```php
// 检查群聊管理权限
$canManageGroup = PermissionHelper::canManageGroup($userId, $groupId);

// 检查组织管理权限
$canManageOrg = PermissionHelper::canManageOrganization($userId, $orgId);

// 检查商家管理权限
$canManageBusiness = PermissionHelper::canManageBusiness($userId, $businessId);

// 检查特定角色
$hasRole = PermissionHelper::hasSpecificRole($userId, 'group_admin', $groupId, 'group');
```

## 控制器中的使用示例

### 示例1：简单权限检查

```php
class ExampleController extends BaseController
{
    public function someAction(Request $request): Json
    {
        $userId = $this->getUserId($request);
        
        // 简单权限检查
        if (!PermissionHelper::hasBasePermission($userId)) {
            return json(['code' => 403, 'message' => '需要完成学生认证']);
        }
        
        // 业务逻辑...
        return json(['code' => 200, 'message' => '操作成功']);
    }
}
```

### 示例2：使用 requirePermission 方法

```php
public function manageGroup(Request $request): Json
{
    try {
        $userId = $this->getUserId($request);
        $groupId = $request->param('group_id', 0, 'intval');
        
        // 权限检查，如果没有权限会抛出异常
        PermissionHelper::requirePermission($userId, 'manage_group', $groupId);
        
        // 权限检查通过，执行业务逻辑
        // ...
        
        return json(['code' => 200, 'message' => '操作成功']);
        
    } catch (\Exception $e) {
        return json(['code' => 403, 'message' => $e->getMessage()]);
    }
}
```

### 示例3：多重权限检查

```php
public function publishActivity(Request $request): Json
{
    $userId = $this->getUserId($request);
    
    // 检查基础权限
    if (!PermissionHelper::hasBasePermission($userId)) {
        return json(['code' => 403, 'message' => '需要完成学生认证']);
    }
    
    // 检查发布权限
    if (!PermissionHelper::canPublishActivity($userId)) {
        return json(['code' => 403, 'message' => '没有活动发布权限']);
    }
    
    // 业务逻辑...
    return json(['code' => 200, 'message' => '发布成功']);
}
```

### 示例4：角色管理

```php
public function grantRole(Request $request): Json
{
    try {
        $userId = $this->getUserId($request);
        $targetUserId = $request->param('target_user_id', 0, 'intval');
        $groupId = $request->param('group_id', 0, 'intval');
        
        // 检查是否有权限授予角色
        PermissionHelper::requirePermission($userId, 'manage_group', $groupId);
        
        // 授予角色
        $result = PermissionHelper::grantRole(
            $targetUserId, 
            'group_admin', 
            $groupId, 
            'group', 
            $userId
        );
        
        if ($result) {
            return json(['code' => 200, 'message' => '角色授予成功']);
        } else {
            return json(['code' => 500, 'message' => '角色授予失败']);
        }
        
    } catch (\Exception $e) {
        return json(['code' => 403, 'message' => $e->getMessage()]);
    }
}
```

## 权限类型说明

### requirePermission 支持的权限类型

- `'base'` - 基础认证权限
- `'admin'` - 管理员权限
- `'publish_activity'` - 活动发布权限
- `'manage_group'` - 群聊管理权限（需要传入 groupId）
- `'manage_organization'` - 组织管理权限（需要传入 orgId）
- `'manage_business'` - 商家管理权限（需要传入 businessId）

### 使用示例

```php
// 基础权限
PermissionHelper::requirePermission($userId, 'base');

// 管理员权限
PermissionHelper::requirePermission($userId, 'admin');

// 群聊管理权限
PermissionHelper::requirePermission($userId, 'manage_group', $groupId);

// 组织管理权限
PermissionHelper::requirePermission($userId, 'manage_organization', $orgId);
```

## 获取权限摘要

```php
// 获取用户完整的权限信息
$summary = PermissionHelper::getUserPermissionSummary($userId);

// 返回格式：
[
    'user_id' => 123,
    'status' => 'verified',
    'has_base_permission' => true,
    'is_admin' => false,
    'is_super_admin' => false,
    'additional_roles_count' => 2,
    'additional_roles' => [...],
    'can_publish_activity' => false,
    'can_manage_official_accounts' => false,
    'can_manage_canteen' => false,
    'has_any_manage_permission' => true
]
```

## 最佳实践

### 1. 在控制器构造函数中检查基础权限

```php
class SomeController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        
        // 如果整个控制器都需要认证
        $userId = $this->getUserId();
        if ($userId && !PermissionHelper::hasBasePermission($userId)) {
            throw new \Exception('需要完成学生认证');
        }
    }
}
```

### 2. 使用异常处理简化代码

```php
public function someAction(Request $request): Json
{
    try {
        $userId = $this->getUserId($request);
        
        // 一次性检查所有必要权限
        PermissionHelper::requirePermission($userId, 'base');
        PermissionHelper::requirePermission($userId, 'manage_group', $groupId);
        
        // 业务逻辑...
        
    } catch (\Exception $e) {
        return json(['code' => 403, 'message' => $e->getMessage()]);
    }
}
```

### 3. 权限检查与业务逻辑分离

```php
private function checkGroupManagePermission(int $userId, int $groupId): void
{
    if (!PermissionHelper::canManageGroup($userId, $groupId)) {
        throw new \Exception('没有群聊管理权限');
    }
}

public function updateGroup(Request $request): Json
{
    try {
        $userId = $this->getUserId($request);
        $groupId = $request->param('group_id', 0, 'intval');
        
        $this->checkGroupManagePermission($userId, $groupId);
        
        // 业务逻辑...
        
    } catch (\Exception $e) {
        return json(['code' => 403, 'message' => $e->getMessage()]);
    }
}
```

这样，你的后端就有了一个类似前端 `roleManager` 的权限管理系统！🎉
