# 权限管理系统使用指南

## 概述

本项目实现了一个基于 **基础身份 + 附加角色** 的混合权限管理系统，支持灵活的权限控制和角色管理。

## 系统架构

### 权限层级

```
基础身份权限（user.status字段）：
├── unverified（未认证）- 最低权限
├── verified（已认证学生）- 基础权限  
├── 管理员（学校管理员）- 对认证学校有管理权限
├── 超级管理员 - 对所有学校有权限
└── 禁言 - 无任何权限

附加角色权限（user_roles表）：
├── group_owner（某群群主）
├── group_admin（某群管理员）
├── official_account_admin（某公众号管理员）
├── club_admin（某社团管理员）
├── business_admin（某商家管理员）
└── institution_admin（某商业机构管理员）
```

### 数据库表结构

#### user_roles表
```sql
CREATE TABLE `user_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `role_type` enum('group_owner','group_admin','official_account_admin','club_admin','business_admin','institution_admin') NOT NULL,
  `target_id` int NOT NULL COMMENT '目标ID（群ID、组织ID等）',
  `target_type` enum('group','organization','official_account','business','institution') NOT NULL,
  `granted_by` int DEFAULT NULL COMMENT '授权人ID',
  `granted_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间（可选）',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-有效 0-无效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_role_target` (`user_id`,`role_type`,`target_id`,`target_type`)
);
```

## 后端使用

### 1. 权限服务类 (PermissionService)

```php
use app\service\PermissionService;

$permissionService = new PermissionService();

// 检查基础权限
$hasBase = $permissionService->hasBasePermission($userId, 'verified');

// 检查角色权限
$hasRole = $permissionService->hasRole($userId, 'group_admin', $groupId, 'group');

// 检查管理权限
$canManage = $permissionService->canManageGroup($userId, $groupId);

// 授予角色
$result = $permissionService->grantRole($userId, 'group_admin', $groupId, 'group', $grantedBy);

// 撤销角色
$result = $permissionService->revokeRole($userId, 'group_admin', $groupId, 'group');
```

### 2. 权限中间件 (CheckPermission)

在路由中使用：
```php
// 基础权限检查
Route::post('api', 'Controller/method')->middleware(['CheckToken', 'CheckPermission:base:verified']);

// 角色权限检查
Route::post('api', 'Controller/method')->middleware(['CheckToken', 'CheckPermission:role:group_admin:group:group_id']);

// 管理权限检查
Route::post('api', 'Controller/method')->middleware(['CheckToken', 'CheckPermission:manage:group:group_id']);

// 管理员权限检查
Route::post('api', 'Controller/method')->middleware(['CheckToken', 'CheckPermission:admin']);
```

在控制器中使用：
```php
use app\middleware\PermissionHelper;

class MyController extends BaseController
{
    public function someMethod(Request $request)
    {
        $userId = $request->middleware('userId');
        
        // 快速权限检查
        if (!PermissionHelper::hasBase($userId, 'verified')) {
            return json(['code' => 403, 'msg' => '需要认证']);
        }
        
        if (!PermissionHelper::canManage($userId, 'group', $groupId)) {
            return json(['code' => 403, 'msg' => '没有管理权限']);
        }
        
        // 业务逻辑...
    }
}
```

### 3. 用户角色管理接口

```php
// 获取用户角色
POST /userRole/getUserRoles
{
    "target_user_id": 123  // 可选，默认查询自己的角色
}

// 授予角色
POST /userRole/grantRole
{
    "target_user_id": 123,
    "role_type": "group_admin",
    "target_id": 456,
    "target_type": "group",
    "expires_at": "2024-12-31 23:59:59"  // 可选
}

// 撤销角色
POST /userRole/revokeRole
{
    "target_user_id": 123,
    "role_type": "group_admin", 
    "target_id": 456,
    "target_type": "group"
}

// 批量权限检查
POST /userRole/checkPermissions
{
    "permissions": [
        {"type": "base", "key": "can_post", "level": "verified"},
        {"type": "role", "key": "can_manage_group", "role_type": "group_admin", "target_id": 123, "target_type": "group"}
    ]
}
```

## 前端使用

### 1. 权限管理器 (roleManager)

```javascript
import roleManager from '../../../utils/roleManager';

// 检查基础权限
const hasBase = roleManager.hasBasePermission();
const isAdmin = roleManager.hasAdminPermission();

// 检查附加角色
const hasRole = roleManager.hasSpecificRole('group_admin', groupId, 'group');

// 检查管理权限
const canManageGroup = roleManager.canManageGroup(groupId);
const canManageOrg = roleManager.canManageOrganization(orgId);

// 获取用户角色
const roles = await roleManager.getUserRoles();

// 刷新角色缓存
await roleManager.refreshUserRoles();

// 批量权限检查
const results = await roleManager.checkMultiplePermissions([
    {type: 'base', key: 'can_post', level: 'verified'},
    {type: 'role', key: 'can_manage', role_type: 'group_admin', target_id: 123, target_type: 'group'}
]);
```

### 2. 页面中的使用示例

```javascript
Page({
    data: {
        canManage: false,
        canGrant: false
    },
    
    async onLoad() {
        await this.checkPermissions();
    },
    
    async checkPermissions() {
        // 刷新用户角色
        await roleManager.refreshUserRoles();
        
        // 检查权限
        const canManage = roleManager.canManageGroup(this.data.groupId);
        const canGrant = roleManager.hasSpecificRole('group_owner', this.data.groupId, 'group') ||
                        roleManager.hasAdminPermission();
        
        this.setData({ canManage, canGrant });
    }
});
```

## 权限配置示例

### 常见权限场景

1. **群聊管理**
   - 群主：可以管理群聊、授予群管理员权限
   - 群管理员：可以管理群聊内容
   - 系统管理员：可以管理所有群聊

2. **组织管理**
   - 社团管理员：可以管理特定组织
   - 系统管理员：可以管理所有组织

3. **内容发布**
   - 已认证用户：可以发布基础内容
   - 特定角色：可以发布特殊内容

### 权限检查流程

1. **基础权限检查**：检查用户的基础身份状态
2. **角色权限检查**：检查用户是否有特定的附加角色
3. **组合权限检查**：基础权限 + 角色权限的组合判断

## 最佳实践

1. **权限最小化原则**：只授予必要的权限
2. **权限分离**：基础权限和附加角色分开管理
3. **缓存策略**：合理使用缓存提高性能
4. **日志记录**：记录重要的权限操作
5. **定期清理**：清理过期的角色权限

## 数据库操作

### 创建用户角色表
```sql
-- 执行建表脚本
mysql -u root -p treehole < mysql/user_roles.sql
```

### 授予群主权限示例
```sql
INSERT INTO user_roles (user_id, role_type, target_id, target_type, granted_by, granted_at, status)
VALUES (123, 'group_owner', 456, 'group', 1, NOW(), 1);
```

### 查询用户权限
```sql
SELECT ur.*, u.username, q.quntitle 
FROM user_roles ur
LEFT JOIN user u ON ur.user_id = u.id
LEFT JOIN qun q ON ur.target_id = q.id AND ur.target_type = 'group'
WHERE ur.user_id = 123 AND ur.status = 1;
```

## 故障排除

1. **权限检查失败**：检查用户角色缓存是否过期
2. **角色授予失败**：检查唯一约束和目标资源是否存在
3. **性能问题**：检查缓存配置和数据库索引

## 权限管理操作指南

### 授予角色权限

#### 通过API接口
```bash
POST /userRole/grantRole
{
    "target_user_id": 123,        # 目标用户ID
    "role_type": "group_admin",   # 角色类型
    "target_id": 456,             # 目标资源ID（如群ID）
    "target_type": "group",       # 目标类型
    "expires_at": "2024-12-31 23:59:59"  # 可选：过期时间
}
```

#### 通过测试页面
1. 进入权限测试页面
2. 点击"测试角色授予"
3. 输入目标用户ID
4. 确认授予权限

### 撤销角色权限

#### 通过API接口
```bash
POST /userRole/revokeRole
{
    "target_user_id": 123,
    "role_type": "group_admin",
    "target_id": 456,
    "target_type": "group"
}
```

#### 通过测试页面
1. 进入权限测试页面
2. 点击"测试角色撤销"
3. 输入目标用户ID
4. 确认撤销权限

### 查看用户角色

#### 查看自己的角色
```bash
POST /userRole/getUserRoles
```

#### 查看其他用户的角色（需要管理员权限）
```bash
POST /userRole/getUserRoles
{
    "target_user_id": 123
}
```

### 权限检查示例

#### 检查群聊管理权限
```javascript
// 前端
const canManage = roleManager.canManageGroup(groupId);

// 后端
$canManage = $permissionService->canManageGroup($userId, $groupId);
```

#### 检查特定角色
```javascript
// 前端
const isGroupAdmin = roleManager.hasSpecificRole('group_admin', groupId, 'group');

// 后端
$isGroupAdmin = $permissionService->hasRole($userId, 'group_admin', $groupId, 'group');
```

## 常见使用场景

### 1. 群聊管理
```sql
-- 设置群主
INSERT INTO user_roles (user_id, role_type, target_id, target_type, granted_by, status)
VALUES (123, 'group_owner', 456, 'group', 1, 1);

-- 设置群管理员
INSERT INTO user_roles (user_id, role_type, target_id, target_type, granted_by, status)
VALUES (124, 'group_admin', 456, 'group', 123, 1);
```

### 2. 社团管理
```sql
-- 设置社团管理员
INSERT INTO user_roles (user_id, role_type, target_id, target_type, granted_by, status)
VALUES (125, 'club_admin', 789, 'organization', 1, 1);
```

### 3. 商家管理
```sql
-- 设置商家管理员
INSERT INTO user_roles (user_id, role_type, target_id, target_type, granted_by, status)
VALUES (126, 'business_admin', 101, 'business', 1, 1);
```

## 权限级别说明

### 基础身份权限（优先级从高到低）
1. **超级管理员** - 所有权限
2. **管理员** - 学校范围内的管理权限
3. **verified** - 已认证学生的基础权限
4. **temporary_verified** - 临时认证用户的基础权限
5. **unverified** - 未认证用户的有限权限
6. **禁言** - 无任何权限

### 附加角色权限
- **group_owner** - 群主，可以管理群聊和授予群管理员权限
- **group_admin** - 群管理员，可以管理群聊内容
- **club_admin** - 社团管理员，可以管理社团事务
- **business_admin** - 商家管理员，可以管理商家信息
- **official_account_admin** - 公众号管理员
- **institution_admin** - 商业机构管理员

## 扩展说明

系统支持轻松扩展新的角色类型和目标类型，只需：
1. 在数据库枚举中添加新类型
2. 在PermissionService中添加对应的检查方法
3. 在前端roleManager中添加对应的权限检查方法

## 测试页面使用说明

权限测试页面位于：`pages/permission_test/permission_test`

### 功能按钮说明
- **测试权限系统** - 测试完整的权限系统后端接口
- **测试角色类型** - 获取系统支持的角色类型
- **测试批量权限** - 测试批量权限检查功能
- **调试Token** - 查看token传递和验证情况
- **测试角色授予** - 给指定用户授予角色权限（需要管理员权限）
- **测试角色撤销** - 撤销指定用户的角色权限（需要管理员权限）
- **清除缓存** - 清除前端角色权限缓存
