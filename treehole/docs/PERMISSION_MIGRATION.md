# 权限系统迁移指南

## 概述

本文档记录了从硬编码状态检查迁移到统一权限管理系统的过程。

## 迁移前后对比

### 前端迁移

#### 原有方式（硬编码）
```javascript
// 检查用户权限
checkPermissions() {
  const userStatus = wx.getStorageSync('status');
  const canPublish = userStatus !== '禁言' && userStatus !== 'unverified';
  this.setData({ canPublish });
}

// 权限检查
if (userStatus === '禁言' || userStatus === 'unverified') {
  wx.showModal({
    title: '权限不足',
    content: '您当前无法发布内容，请联系管理员'
  });
}
```

#### 新方式（权限管理）
```javascript
import roleManager from '../../../utils/roleManager';

// 检查用户权限
checkPermissions() {
  const canPublish = roleManager.hasBasePermission();
  this.setData({ canPublish });
}

// 权限检查
if (!roleManager.hasBasePermission()) {
  wx.showModal({
    title: '权限不足',
    content: '您需要完成学生认证才能发布内容'
  });
}
```

### 后端迁移

#### 原有方式（硬编码）
```php
// 检查用户状态
$user = Db::name('user')->where('id', $userId)->find();
if (!$user || $user['status'] === '禁言' || $user['status'] === 'unverified') {
    return json(['code' => 403, 'msg' => '您当前无法发布内容']);
}

// 复杂的状态检查
if ($user['status'] !== 'verified' && $user['status'] !== '管理员' && 
    $user['status'] !== 'temporary_verified' && $user['status'] !== '社团管理员') {
    return json(['code' => 403, 'message' => '请先完成认证后再操作']);
}
```

#### 新方式（权限管理）
```php
use app\util\PermissionHelper;

// 简单权限检查
if (!PermissionHelper::hasBasePermission($userId)) {
    return json(['code' => 403, 'msg' => '您需要完成学生认证才能发布内容']);
}

// 使用异常处理
try {
    PermissionHelper::requirePermission($userId, 'base');
    // 业务逻辑...
} catch (\Exception $e) {
    return json(['code' => 403, 'msg' => $e->getMessage()]);
}
```

## 已完成的迁移

### 前端文件
- ✅ `pages/liaoran/list/index.js` - 权限检查方法
- ✅ `pages/liaoran/publish/index.js` - 权限检查和提示信息

### 后端文件
- ✅ `app/controller/Message.php` - 发布消息权限检查
- ✅ `app/controller/Liaoran.php` - 所有权限检查方法
  - 添加评分对象
  - 添加评分
  - 添加评论
  - 添加分类
  - 添加对象
- ✅ `app/controller/Comment.php` - 发布回复权限检查
- ✅ `app/controller/Qun.php` - 群聊相关权限检查
- ✅ `app/controller/Auth.php` - 认证申请和管理员权限检查
- ✅ `app/controller/Email.php` - 邮箱更新权限检查
- ✅ `app/controller/Sen.php` - 学生认证权限检查

## 迁移的好处

### 1. **统一性**
- 所有权限检查都使用相同的方法
- 前后端权限逻辑保持一致

### 2. **可维护性**
- 权限逻辑集中管理
- 修改权限规则只需要在一个地方修改

### 3. **可扩展性**
- 支持复杂的权限组合
- 支持附加角色权限

### 4. **用户体验**
- 统一的错误提示信息
- 更清晰的权限说明

## 权限检查方法对照表

| 原有检查 | 新方法 | 说明 |
|---------|--------|------|
| `status !== '禁言' && status !== 'unverified'` | `roleManager.hasBasePermission()` / `PermissionHelper::hasBasePermission()` | 基础认证权限 |
| `status === '管理员'` | `roleManager.hasAdminPermission()` / `PermissionHelper::hasAdminPermission()` | 管理员权限 |
| `status === '社团管理员'` | `roleManager.isClubAdmin()` / `PermissionHelper::isClubAdmin()` | 社团管理员权限 |
| `in_array($status, ['admin', '管理员'])` | `PermissionHelper::hasAdminPermission()` | 管理员权限检查 |
| `status === 'verified'` | `PermissionHelper::hasBasePermission()` | 已认证权限 |
| 复杂状态组合 | `roleManager.canPublishActivity()` / `PermissionHelper::canPublishActivity()` | 活动发布权限 |

## 后续建议

### 1. **继续迁移**
还有一些地方可能需要继续迁移：
- 其他控制器中的硬编码状态检查
- 前端其他页面的权限检查

### 2. **权限细化**
可以考虑将更多功能权限细化：
- 评论权限
- 点赞权限
- 上传权限

### 3. **权限缓存**
对于频繁的权限检查，可以考虑添加缓存机制。

### 4. **权限日志**
可以添加权限检查的日志记录，便于调试和审计。

## 注意事项

### 1. **向后兼容**
- 新的权限系统完全向后兼容
- 原有的权限检查方法依然可用

### 2. **测试**
- 迁移后需要充分测试各种权限场景
- 确保权限检查逻辑正确

### 3. **文档更新**
- 更新相关的开发文档
- 培训开发团队使用新的权限系统

## 总结

通过这次迁移，我们：
1. ✅ 消除了大量硬编码的状态检查
2. ✅ 建立了统一的权限管理系统
3. ✅ 提高了代码的可维护性和可扩展性
4. ✅ 为未来的权限功能扩展奠定了基础

权限管理系统现在更加健壮、灵活，能够满足未来业务发展的需要。
