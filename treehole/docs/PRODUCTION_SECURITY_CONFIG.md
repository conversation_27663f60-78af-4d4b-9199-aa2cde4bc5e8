# 🛡️ 生产环境安全配置指南

## 📋 配置概述

生产环境安全监控采用"**只告警，不封禁**"的策略，确保系统稳定性的同时提供完整的安全监控。

## 🔧 核心配置

### 1. 安全策略调整

#### **修改前（自动封禁）**
- 检测到威胁 → 自动加入黑名单 → 阻断访问
- 风险：可能误封正常用户

#### **修改后（只告警）**
- 检测到威胁 → 记录安全事件 → 发送告警邮件
- 优势：保证服务可用性，人工判断处理

### 2. 告警机制

#### **实时告警**
- 高频请求：超过阈值立即发送邮件
- 可疑User-Agent：检测到攻击工具立即告警
- 可疑路径访问：访问敏感路径立即告警

#### **汇总告警**
- 单IP当天事件达到10次时发送汇总告警
- 每10次事件发送一次汇总（避免邮件轰炸）

## 📊 监控功能

### 1. 安全事件记录
```php
// 记录结构
$securityEvents = [
    '2025-08-12' => [
        '*************' => [
            'first_event' => 1691836800,
            'last_event' => 1691837400,
            'event_count' => 15,
            'total_severity' => 25,
            'events' => ['高频请求', '可疑User-Agent', ...]
        ]
    ]
];
```

### 2. 告警邮件内容
```
🚨 安全事件汇总告警 - IP: *************

事件总数：15
严重程度：25
首次事件：2025-08-12 10:00:00
最后事件：2025-08-12 10:10:00

事件详情：
1. 高频请求
2. 可疑User-Agent: sqlmap
3. 访问可疑路径: /admin

建议：请检查该IP的行为是否正常，必要时可手动封禁。
```

## 🛠️ 管理工具

### 1. 安全监控命令
```bash
# 启动安全监控管理工具
php think security:monitor
```

#### **功能菜单**
1. **查看安全事件统计** - 总体数据概览
2. **查看今日安全事件** - 当天详细事件
3. **查看IP黑名单** - 当前封禁列表
4. **手动封禁IP** - 紧急情况下手动封禁
5. **解封IP** - 解除误封或恢复访问
6. **清理过期事件** - 清理7天前的记录
7. **发送测试告警** - 测试邮件系统

### 2. 使用示例

#### **查看今日安全事件**
```
=== 今日安全事件 ===
IP: *************
  事件数：12
  严重度：18
  首次：09:15:30
  最后：09:45:20
  事件：高频请求, 可疑User-Agent, 可疑路径访问
```

#### **手动封禁IP**
```
请输入要封禁的IP地址: *************
请输入封禁原因: 确认为恶意攻击
✅ IP ************* 已被手动封禁
```

## ⚙️ 配置参数

### 1. 告警阈值配置
在 `config/secrets.php` 中设置：
```php
'security' => [
    'admin_email' => '<EMAIL>',
    'max_requests_per_minute' => 60,        // 每分钟最大请求数
    'enable_auto_ban' => false,             // 禁用自动封禁
    'alert_threshold' => 10,                // 汇总告警阈值
    'event_retention_days' => 7,            // 事件保留天数
]
```

### 2. 开发环境白名单
```php
'security' => [
    'dev_mode' => false,                    // 生产环境关闭开发模式
    'local_ips' => [],                      // 生产环境清空本地IP白名单
    'wechat_dev_skip' => false,             // 生产环境不跳过微信开发工具
]
```

## 📧 邮件服务配置

### 1. 确保邮件服务正常
```bash
# 测试邮件发送
php think security:monitor
# 选择 "7. 发送测试告警"
```

### 2. 邮件服务器配置
在 `app/service/system/AlertService.php` 中配置SMTP：
```php
$mail->isSMTP();
$mail->Host = 'smtp.qq.com';           // 你的SMTP服务器
$mail->SMTPAuth = true;
$mail->Username = '<EMAIL>'; // 发件邮箱
$mail->Password = 'your-auth-code';     // 授权码
$mail->SMTPSecure = 'ssl';
$mail->Port = 465;
```

## 🔍 日常运维

### 1. 每日检查
```bash
# 查看今日安全事件
php think security:monitor
# 选择 "2. 查看今日安全事件"

# 查看系统日志
tail -f logs/php_error.log | grep "安全"
```

### 2. 每周维护
```bash
# 清理过期事件
php think security:monitor
# 选择 "6. 清理过期事件"

# 查看安全统计
php think security:monitor
# 选择 "1. 查看安全事件统计"
```

### 3. 紧急处理
```bash
# 发现恶意IP时手动封禁
php think security:monitor
# 选择 "4. 手动封禁IP"

# 误封时解除封禁
php think security:monitor
# 选择 "5. 解封IP"
```

## 📊 监控指标

### 1. 关键指标
- **每日安全事件数量**
- **涉及的唯一IP数量**
- **高频请求告警次数**
- **可疑User-Agent检测次数**
- **手动封禁IP数量**

### 2. 告警级别
- **🟢 Info**：正常记录，无需处理
- **🟡 Warning**：需要关注，可能需要处理
- **🔴 Critical**：严重威胁，需要立即处理

## 🚨 应急响应

### 1. 大量攻击时
```bash
# 1. 查看当前攻击情况
php think security:monitor

# 2. 手动封禁主要攻击IP
# 选择 "4. 手动封禁IP"

# 3. 临时启用自动封禁（紧急情况）
# 编辑 app/middleware/SecurityMonitor.php
# 取消 addToBlacklist 方法中的注释
```

### 2. 系统异常时
```bash
# 1. 检查缓存系统
php think cache:monitor

# 2. 检查邮件系统
php think security:monitor
# 选择 "7. 发送测试告警"

# 3. 查看错误日志
tail -f logs/php_error.log
```

## 📈 性能优化

### 1. 缓存优化
- 安全事件记录保存7天
- 使用Redis缓存，故障时自动降级到文件缓存
- 定期清理过期数据

### 2. 告警优化
- 防止重复告警（5分钟内同IP不重复发送）
- 汇总告警机制（避免邮件轰炸）
- 智能过滤开发环境请求

---

**注意**：生产环境部署后，建议先观察1-2天的告警情况，根据实际情况调整阈值和规则。
