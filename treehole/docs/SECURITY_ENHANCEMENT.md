# 安全加固文档

## 概述

本文档记录了对消息接口的安全加固，主要是为缺少 token 验证的接口添加身份验证。

## 修复的安全问题

### 1. 消息接口缺少 token 验证

#### 问题描述
以下接口缺少 token 验证，任何人都可以访问：
- `Message::getMessages()` - 获取消息列表
- `Message::searchMessages()` - 搜索消息
- `Message::getOneUserMessage()` - 获取用户消息

#### 安全风险
- 未授权访问用户数据
- 可能泄露敏感信息
- 绕过用户权限控制

#### 修复方案
为所有消息接口添加 token 验证：

```php
// Token验证
$token = $request->header('token');
if (!$token) {
    return json(['error_code' => 1, 'msg' => '请先登录']);
}

$userData = JwtUtil::validateToken($token);
if (!$userData) {
    return json(['error_code' => 1, 'msg' => 'token无效或已过期']);
}

$currentUserId = $userData['user_id'];
```

## 修复的接口

### 1. Message::getMessages()
**文件**: `app/controller/Message.php`
**行数**: 392-409
**修复内容**:
- 添加 token 验证
- 获取当前用户ID

### 2. Message::searchMessages()
**文件**: `app/controller/Message.php`
**行数**: 661-677
**修复内容**:
- 添加 token 验证
- 获取当前用户ID

### 3. Message::getOneUserMessage()
**文件**: `app/controller/Message.php`
**行数**: 549-568
**修复内容**:
- 添加 token 验证
- 添加权限检查（只能查看自己的消息，管理员除外）
- 获取当前用户ID

## 前端适配

### 1. 更新请求头
为相关的前端调用添加 token：

#### home.js
```javascript
header: {
  'content-type': 'application/x-www-form-urlencoded',
  'token': wx.getStorageSync('access_token')  // 添加token
}
```

#### yijian.js
```javascript
header: {
  'content-type': 'application/x-www-form-urlencoded',
  'token': wx.getStorageSync('access_token')  // 添加token
}
```

### 2. 错误处理
前端需要处理新的错误响应：
- `error_code: 1` - 未登录或token无效
- `error_code: 3` - 权限不足

## 中间件更新

### 禁言检查中间件
从白名单中移除了 `/message/getMessageList`，因为现在这些接口需要登录验证。

**文件**: `app/middleware/CheckBanned.php`
**修改**: 移除消息相关的白名单路径

## 权限控制增强

### getOneUserMessage 权限检查
```php
// 权限检查：只能查看自己的消息，或者管理员可以查看所有人的消息
if ($userId != $currentUserId && !PermissionHelper::hasAdminPermission($currentUserId)) {
    return json(['error_code' => 3, 'msg' => '没有权限查看其他用户的消息']);
}
```

## 安全检查清单

### ✅ 已完成
- [x] Message::getMessages() 添加 token 验证
- [x] Message::searchMessages() 添加 token 验证
- [x] Message::getOneUserMessage() 添加 token 验证和权限检查
- [x] Message::getHotMessages() 添加 token 验证
- [x] Message::getMessageDetails() 添加 token 验证
- [x] Message::getVoteDetails() 添加 token 验证
- [x] Comment::getComments() 添加 token 验证
- [x] 前端调用添加 token 传递
- [x] 更新禁言中间件白名单（严格限制禁言用户访问）
- [x] 删除帖子权限检查优化

### 🔍 建议继续检查
- [ ] 其他可能缺少 token 验证的接口
- [ ] 文件上传接口的安全性
- [ ] 管理员接口的权限控制
- [ ] 敏感数据的访问控制

## 测试建议

### 1. 功能测试
- 验证已登录用户可以正常访问消息接口
- 验证未登录用户被正确拒绝
- 验证 token 过期时的处理

### 2. 权限测试
- 验证用户只能查看自己的消息
- 验证管理员可以查看所有用户的消息
- 验证禁言用户被正确拦截

### 3. 安全测试
- 尝试使用无效 token 访问接口
- 尝试访问其他用户的私有数据
- 验证错误信息不泄露敏感信息

## 影响评估

### 正面影响
- ✅ 提高了系统安全性
- ✅ 防止了未授权访问
- ✅ 统一了权限控制逻辑

### 潜在影响
- ⚠️ 需要前端适配（已完成）
- ⚠️ 可能影响现有的第三方调用（如果有）
- ⚠️ 需要确保所有调用都传递了正确的 token

## 后续建议

### 1. 安全审计
定期审计所有接口的权限控制，确保：
- 所有需要登录的接口都有 token 验证
- 敏感操作有适当的权限检查
- 错误信息不泄露系统信息

### 2. 监控告警
添加安全监控：
- 记录未授权访问尝试
- 监控异常的 token 使用
- 告警可疑的访问模式

### 3. 文档维护
- 更新 API 文档，标明需要 token 的接口
- 维护权限控制的设计文档
- 记录安全相关的变更

## 总结

通过这次安全加固，我们：
1. ✅ 修复了消息接口的安全漏洞
2. ✅ 统一了权限验证逻辑
3. ✅ 提高了系统的整体安全性
4. ✅ 保持了向后兼容性

系统现在更加安全，所有消息相关的操作都需要适当的身份验证和权限检查。
