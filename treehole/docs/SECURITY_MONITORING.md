# 🛡️ 安全监控系统使用指南

## 📋 系统概述

树洞项目的安全监控系统包含以下组件：
- **定期安全扫描**：自动检测恶意数据和可疑行为
- **实时异常监控**：监控异常请求和攻击尝试
- **告警通知系统**：及时通知管理员安全威胁
- **自动化清理**：自动清理恶意数据

## 🚀 快速开始

### 1. 配置管理员邮箱
编辑 `config/secrets.php`，设置你的邮箱：
```php
'security' => [
    'admin_email' => '<EMAIL>', // 已配置
    'alert_enabled' => true,
    'email_from' => '<EMAIL>',
    'email_from_name' => '树洞安全监控系统',
],
```

### 2. 给脚本执行权限
```bash
chmod +x scripts/security_monitor.sh
```

### 3. 设置定时任务
```bash
# 复制定时任务模板
cp scripts/crontab.example /tmp/treehole_crontab

# 自动替换路径
sed -i 's|/path/to/treehole|'$(pwd)'|g' /tmp/treehole_crontab

# 安装定时任务
crontab /tmp/treehole_crontab
```

## 🔧 手动执行

### 执行安全扫描
```bash
php think security:scan
```

### 清理恶意用户名
```bash
# 推荐使用Command方式
php think security:clean

# 或使用旧脚本（已迁移）
php clean_malicious_usernames.php
```

### 执行监控脚本
```bash
# 每日扫描
./scripts/security_monitor.sh daily

# 每小时检查
./scripts/security_monitor.sh hourly

# 每周深度扫描
./scripts/security_monitor.sh weekly
```

### 检查系统状态
```bash
# 查看安全日志
tail -f logs/php_error.log | grep "SQL注入攻击尝试"

# 查看扫描报告
cat logs/security_scan_$(date +%Y-%m-%d).json
```

## 📊 监控指标

### 自动检测的威胁类型
1. **SQL注入攻击**
   - 恶意用户名
   - 危险字符组合
   - SQL注入模式

2. **异常请求行为**
   - 高频请求（1分钟超过100次）
   - 可疑User-Agent
   - 访问敏感路径

3. **登录安全**
   - 失败登录次数统计
   - IP黑名单管理

4. **内容安全**
   - 可疑帖子内容
   - 恶意脚本检测

### 告警级别
- **🚨 Critical（紧急）**：发现5个以上恶意用户或50个以上异常请求
- **⚠️ Warning（警告）**：发现少量安全威胁
- **ℹ️ Info（信息）**：正常扫描结果

## 📧 告警通知

### 邮件告警内容
- 威胁统计信息
- 详细的安全事件列表
- 建议的处理措施
- 相关日志文件位置

### 实时告警触发条件
- SQL注入攻击尝试
- 高频异常请求
- 磁盘空间不足
- 系统异常错误

## 📁 日志文件说明

### 主要日志文件
```
logs/
├── php_error.log              # 系统错误日志
├── security_scan_YYYY-MM-DD.json  # 每日扫描报告
├── security_cron.log          # 定时任务日志
└── runtime/                   # ThinkPHP运行时日志
```

### 日志清理策略
- 安全扫描报告：保留7天
- 错误日志：30天后压缩，90天后删除
- 运行时日志：自动轮转

## 🛠️ 自定义配置

### 修改扫描频率
编辑 `config/secrets.php`：
```php
'security' => [
    'scan_frequency' => 'daily', // daily, weekly
    'max_failed_logins' => 10,   // 失败登录阈值
    'max_requests_per_minute' => 100, // 请求频率阈值
],
```

### 添加自定义检测规则
编辑 `app/command/SecurityScan.php`，在相应方法中添加检测逻辑。

### 自定义告警方式
编辑 `app/service/AlertService.php`，可以添加：
- 短信通知
- 微信通知
- 钉钉通知
- Slack通知

## 🚨 应急响应

### 发现安全威胁时的处理步骤

1. **立即响应**
   ```bash
   # 查看详细日志
   tail -100 logs/php_error.log
   
   # 执行紧急扫描
   php think security:scan
   ```

2. **清理恶意数据**
   ```bash
   # 清理恶意用户名
   php clean_malicious_usernames.php
   
   # 检查数据库
   mysql -u username -p database_name
   ```

3. **加强防护**
   - 临时降低请求频率限制
   - 更新IP黑名单
   - 检查防火墙规则

4. **事后分析**
   - 分析攻击模式
   - 更新检测规则
   - 加强相关防护

## 📈 性能优化

### 缓存策略
- 使用Redis缓存检测结果
- 避免重复扫描相同数据
- 合理设置缓存过期时间

### 扫描优化
- 分批处理大量数据
- 使用索引优化数据库查询
- 异步处理非紧急任务

## 🔍 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查邮箱配置
   - 确认SMTP设置
   - 查看错误日志

2. **定时任务不执行**
   - 检查crontab配置
   - 确认脚本权限
   - 查看系统日志

3. **扫描结果异常**
   - 检查数据库连接
   - 确认Redis服务状态
   - 查看ThinkPHP日志

### 调试模式
```bash
# 启用详细日志
export DEBUG=1
php think security:scan

# 查看中间件日志
tail -f logs/php_error.log | grep "SecurityMonitor"
```

## 📞 技术支持

如果遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件设置
3. 确认系统环境要求
4. 联系技术支持团队

---

**注意**：定期更新安全规则和检测模式，保持系统安全防护的有效性。
