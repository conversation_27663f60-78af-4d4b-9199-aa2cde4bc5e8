# 禁言系统设计文档

## 概述

禁言系统通过全局中间件实现，对被禁言的用户进行统一拦截，确保他们无法进行任何需要权限的操作。

## 系统架构

### 1. 全局中间件拦截
- **中间件**: `app\middleware\CheckBanned`
- **位置**: 全局中间件，在所有请求处理前执行
- **优先级**: 在安全检查之后，业务逻辑之前

### 2. 白名单机制
允许禁言用户访问的接口：
- 登录相关接口
- 获取基础信息接口（只读）
- 静态资源接口
- 系统配置接口

### 3. 前端统一处理
- 请求拦截器统一处理禁言状态
- 显示友好的禁言提示

## 实现细节

### 后端中间件

```php
// app/middleware/CheckBanned.php
class CheckBanned
{
    // 白名单路径
    private $whitelist = [
        '/user/login',
        '/user/verifyStatus',
        '/user/getUserInfo',
        // ... 其他只读接口
    ];
    
    public function handle(Request $request, \Closure $next)
    {
        // 1. 检查白名单
        // 2. 验证token
        // 3. 查询用户状态
        // 4. 拦截禁言用户
    }
}
```

### 前端处理

```javascript
// utils/request.js 响应拦截器
const responseInterceptor = (response) => {
  const { error_code, data, msg } = response.data;
  
  // 处理禁言状态
  if (error_code === 403 && data && data.banned) {
    wx.showModal({
      title: '账号已被禁言',
      content: msg,
      showCancel: false
    });
    return Promise.reject(new Error('用户被禁言'));
  }
  
  // ... 其他处理
};
```

## 白名单接口

### 登录相关
- `/user/login` - 用户登录
- `/user/register` - 用户注册
- `/user/wechatLogin` - 微信登录
- `/user/refreshToken` - 刷新token

### 信息获取（仅限必要的状态查询）
- `/user/verifyStatus` - 验证用户状态
- `/user/getUserInfo` - 获取用户信息

**注意**: 禁言用户不能访问任何内容浏览接口，包括：
- ❌ 消息列表、热门消息、消息详情
- ❌ 评论列表、评论详情
- ❌ 群聊列表、学校列表
- ❌ 评分列表、评分详情
- ❌ 系统配置、通知列表

### 系统接口
- `/system/getConfig` - 获取系统配置
- `/notification/getList` - 获取通知列表
- `/upload/getUploadToken` - 获取上传token

## 禁言用户体验

### 1. 接口访问
- ✅ **允许**: 登录、获取自己的状态信息
- ❌ **禁止**: 浏览内容、发布、评论、点赞、上传等所有其他操作

### 2. 错误提示
```json
{
  "error_code": 403,
  "msg": "您已被禁言，无法进行此操作。如有疑问请联系管理员。",
  "data": {
    "banned": true,
    "user_status": "禁言"
  }
}
```

### 3. 前端显示
- 统一的禁言提示弹窗
- 清晰的错误说明
- 引导联系管理员

## 管理员操作

### 1. 设置禁言
```sql
UPDATE user SET status = '禁言' WHERE id = {user_id};
```

### 2. 解除禁言
```sql
UPDATE user SET status = 'verified' WHERE id = {user_id};
-- 或根据用户之前的状态设置为相应值
```

### 3. 批量操作
```sql
-- 批量禁言
UPDATE user SET status = '禁言' WHERE id IN (1,2,3);

-- 批量解禁
UPDATE user SET status = 'verified' WHERE status = '禁言';
```

## 系统优势

### 1. 统一拦截
- 无需在每个接口单独检查
- 避免遗漏和不一致
- 集中管理，易于维护

### 2. 性能优化
- 早期拦截，减少不必要的业务处理
- 缓存用户状态，减少数据库查询

### 3. 用户体验
- 统一的错误提示
- 清晰的禁言说明
- 保留基础浏览功能

### 4. 安全性
- 全面覆盖所有写操作
- 防止绕过检查
- 日志记录便于审计

## 扩展功能

### 1. 临时禁言
```php
// 可以扩展支持临时禁言
$user['ban_until'] = '2024-12-31 23:59:59';
if ($user['status'] === '禁言' && 
    $user['ban_until'] && 
    strtotime($user['ban_until']) > time()) {
    // 仍在禁言期内
}
```

### 2. 禁言原因
```php
// 可以记录禁言原因
$user['ban_reason'] = '发布违规内容';
```

### 3. 分级禁言
```php
// 可以实现不同级别的禁言
$user['ban_level'] = 1; // 1: 禁止发布, 2: 禁止评论, 3: 完全禁言
```

## 注意事项

### 1. 白名单维护
- 新增接口时考虑是否需要加入白名单
- 定期审查白名单的合理性
- 避免将写操作接口加入白名单

### 2. 性能考虑
- 考虑缓存用户状态
- 避免每次请求都查询数据库
- 可以使用Redis缓存用户状态

### 3. 日志记录
- 记录禁言用户的访问尝试
- 便于分析和审计
- 帮助发现系统问题

### 4. 错误处理
- 中间件异常时应该放行请求
- 避免因禁言检查导致系统不可用
- 提供降级方案

## 总结

禁言系统通过全局中间件实现了：
- ✅ 统一的禁言用户拦截
- ✅ 灵活的白名单机制
- ✅ 友好的用户体验
- ✅ 完善的日志记录
- ✅ 易于维护和扩展

这种设计确保了禁言功能的全面性和一致性，同时保持了系统的性能和用户体验。
