# 🪟 Windows定时任务安装和管理指南

## 📋 概述

本指南介绍如何在Windows环境下为树洞项目安装和管理定时任务。

## 🚀 快速开始

### 1. 安装定时任务

**以管理员身份运行PowerShell**，然后执行：

```powershell
# 进入项目目录
cd C:\path\to\your\treehole

# 执行安装脚本
.\scripts\install_windows_tasks.ps1
```

### 2. 验证安装

```powershell
# 查看已安装的任务
.\scripts\manage_windows_tasks.ps1 -Action list
```

## 📊 定时任务列表

| 任务名称 | 描述 | 执行频率 | 执行时间 |
|---------|------|---------|---------|
| TreeHole-SecurityScan | 安全扫描 | 每天 | 凌晨2:00 |
| TreeHole-CacheMonitor | 缓存监控 | 每30分钟 | 全天 |
| TreeHole-WeeklyReport | 周报生成 | 每周一 | 上午9:00 |
| TreeHole-EventCleanup | 事件清理 | 每天 | 凌晨6:00 |

## 🛠️ 管理命令

### 查看任务列表
```powershell
.\scripts\manage_windows_tasks.ps1 -Action list
```

### 查看任务状态
```powershell
.\scripts\manage_windows_tasks.ps1 -Action status
```

### 启动任务
```powershell
# 启动所有任务
.\scripts\manage_windows_tasks.ps1 -Action start

# 启动指定任务
.\scripts\manage_windows_tasks.ps1 -Action start -TaskName SecurityScan
```

### 停止任务
```powershell
# 停止所有任务
.\scripts\manage_windows_tasks.ps1 -Action stop

# 停止指定任务
.\scripts\manage_windows_tasks.ps1 -Action stop -TaskName CacheMonitor
```

### 删除任务
```powershell
# 删除所有任务
.\scripts\manage_windows_tasks.ps1 -Action remove

# 删除指定任务
.\scripts\manage_windows_tasks.ps1 -Action remove -TaskName WeeklyReport
```

### 手动测试任务
```powershell
# 测试安全扫描
.\scripts\manage_windows_tasks.ps1 -Action test -TaskName SecurityScan

# 测试缓存监控
.\scripts\manage_windows_tasks.ps1 -Action test -TaskName CacheMonitor
```

## 🔧 高级管理

### 使用Windows任务计划程序

1. **打开任务计划程序**
   ```
   开始菜单 → 搜索"任务计划程序" → 打开
   ```

2. **查找树洞任务**
   - 在左侧面板选择"任务计划程序库"
   - 查找以"TreeHole-"开头的任务

3. **查看任务详情**
   - 双击任务名称
   - 查看"常规"、"触发器"、"操作"等选项卡

4. **查看执行历史**
   - 选择任务
   - 点击"历史记录"选项卡
   - 查看任务执行日志

### 修改任务设置

1. **修改执行时间**
   - 右键点击任务 → "属性"
   - 切换到"触发器"选项卡
   - 编辑现有触发器

2. **修改执行用户**
   - 切换到"常规"选项卡
   - 点击"更改用户或组"

3. **修改执行条件**
   - 切换到"条件"选项卡
   - 设置电源、网络等条件

## 🐛 故障排除

### 常见问题

#### 1. 任务创建失败
**错误**：权限不足
**解决**：以管理员身份运行PowerShell

#### 2. 任务不执行
**检查项**：
- 计算机是否在任务执行时间开机
- 用户是否已登录
- PHP是否在系统PATH中

#### 3. PHP命令找不到
**解决方法**：
```powershell
# 检查PHP是否可用
php -v

# 如果不可用，添加PHP到系统PATH
# 或在任务中使用完整路径，如：C:\php\php.exe
```

### 查看执行日志

#### 1. Windows事件日志
```powershell
# 查看任务计划程序日志
Get-WinEvent -LogName "Microsoft-Windows-TaskScheduler/Operational" | Where-Object {$_.Message -like "*TreeHole*"}
```

#### 2. 项目日志
```powershell
# 查看项目日志文件
Get-Content logs\php_error.log -Tail 20
```

#### 3. 手动执行测试
```powershell
# 手动执行命令测试
cd C:\path\to\your\treehole
php think security:scan
```

## 📝 最佳实践

### 1. 定期检查
- 每周检查任务执行状态
- 查看执行历史记录
- 监控系统资源使用

### 2. 备份配置
```powershell
# 导出任务配置
schtasks /query /tn "TreeHole-SecurityScan" /xml > TreeHole-SecurityScan.xml
```

### 3. 性能优化
- 避免在高峰时段执行资源密集型任务
- 合理设置任务执行间隔
- 监控任务执行时间

### 4. 安全考虑
- 使用最小权限用户运行任务
- 定期更新任务配置
- 监控异常执行情况

## 🔄 更新和维护

### 代码更新后
- **通常不需要重新安装任务**
- 任务会自动使用更新后的代码
- 只有修改任务时间或添加新任务时才需要重新安装

### 重新安装任务
```powershell
# 删除现有任务
.\scripts\manage_windows_tasks.ps1 -Action remove

# 重新安装
.\scripts\install_windows_tasks.ps1
```

### 系统重启后
- 任务会自动恢复
- 无需手动干预
- 建议重启后检查任务状态

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查Windows事件日志
3. 手动执行命令测试
4. 查看项目日志文件

---

## 🎯 总结

- ✅ 使用PowerShell脚本自动化管理
- ✅ 支持完整的任务生命周期管理
- ✅ 提供详细的状态监控和日志
- ✅ 兼容Windows任务计划程序
- ✅ 支持手动测试和故障排除
