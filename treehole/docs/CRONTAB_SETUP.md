# ⏰ 定时任务配置指南

## 📋 概述

树洞安全监控系统需要定时任务来自动执行各种监控和维护任务。本指南将帮你快速配置所有必要的定时任务。

## 🚀 快速安装

### 方法1：自动安装（推荐）
```bash
# 在项目根目录执行
./scripts/install_crontab.sh
```

### 方法2：手动安装
```bash
# 1. 复制配置文件
cp scripts/crontab.example /tmp/treehole_crontab

# 2. 替换路径（将 /path/to/your/project 替换为实际路径）
sed -i 's|/path/to/treehole|/path/to/your/project|g' /tmp/treehole_crontab

# 3. 安装定时任务
crontab /tmp/treehole_crontab

# 4. 验证安装
crontab -l
```

## 📊 定时任务列表

### 🛡️ 安全监控任务
```bash
# 每天凌晨2点执行安全扫描
0 2 * * * /path/to/treehole/scripts/security_monitor.sh daily

# 每小时检查异常请求和系统状态  
0 * * * * /path/to/treehole/scripts/security_monitor.sh hourly

# 每周日凌晨3点执行深度扫描
0 3 * * 0 /path/to/treehole/scripts/security_monitor.sh weekly
```

### 💾 缓存监控任务
```bash
# 每30分钟检查缓存系统状态
*/30 * * * * cd /path/to/treehole && php think cache:monitor
```

### 📧 报告任务
```bash
# 每周一上午9点发送周报
0 9 * * 1 cd /path/to/treehole && php think report:weekly
```

### 🧹 维护任务
```bash
# 每天凌晨5点轮转日志文件
0 5 * * * cd /path/to/treehole && find logs/ -name "*.log" -size +100M -exec mv {} {}.$(date +%Y%m%d) \;

# 每天凌晨6点清理过期安全事件
0 6 * * * cd /path/to/treehole && php think security:monitor --clean
```

## 🔧 任务详细说明

### 1. 安全扫描任务
- **daily**：每日安全扫描，检测恶意用户、可疑帖子等
- **hourly**：每小时检查，监控异常请求和系统状态
- **weekly**：每周深度扫描，生成详细报告

### 2. 缓存监控任务
- 检查Redis服务状态
- 自动切换到文件缓存（如果Redis不可用）
- 清理过期的文件缓存
- 发送Redis故障告警

### 3. 报告任务
- 生成安全监控周报
- 发送到管理员邮箱
- 包含完整的安全统计和建议

### 4. 维护任务
- 日志文件轮转（防止日志文件过大）
- 清理过期的安全事件记录
- 保持系统性能

## 📊 监控和日志

### 查看定时任务日志
```bash
# 查看所有定时任务日志
tail -f logs/cron.log

# 查看最近的定时任务执行情况
tail -100 logs/cron.log

# 查看特定任务的日志
grep "security:scan" logs/cron.log
```

### 查看系统日志
```bash
# 查看PHP错误日志
tail -f logs/php_error.log

# 查看安全告警
grep "告警" logs/php_error.log

# 查看缓存状态
grep "缓存" logs/php_error.log
```

### 查看安全扫描报告
```bash
# 查看最新的扫描报告
ls -la logs/security_scan_*.json | tail -5

# 查看具体报告内容
cat logs/security_scan_$(date +%Y-%m-%d).json | jq .
```

## 🛠️ 管理命令

### 查看定时任务
```bash
# 查看当前用户的定时任务
crontab -l

# 查看定时任务执行状态
systemctl status crond  # CentOS/RHEL
systemctl status cron   # Ubuntu/Debian
```

### 手动执行任务
```bash
# 手动执行安全扫描
./scripts/security_monitor.sh daily

# 手动检查缓存
php think cache:monitor

# 手动生成周报
php think report:weekly

# 手动清理过期事件
php think security:monitor --clean
```

### 管理安全监控
```bash
# 启动安全监控管理界面
php think security:monitor

# 查看今日安全事件
php think security:monitor
# 然后选择 "2. 查看今日安全事件"
```

## ⚠️ 故障排除

### 1. 定时任务不执行
```bash
# 检查cron服务状态
systemctl status crond

# 检查定时任务语法
crontab -l | head -20

# 查看系统日志
tail -f /var/log/cron
```

### 2. 脚本权限问题
```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 检查文件权限
ls -la scripts/
```

### 3. PHP命令找不到
```bash
# 查找PHP路径
which php

# 在定时任务中使用完整路径
/usr/bin/php think cache:monitor
```

### 4. 邮件发送失败
```bash
# 测试邮件发送
php think security:monitor
# 选择 "7. 发送测试告警"

# 检查邮件配置
grep -r "smtp" app/service/system/AlertService.php
```

## 📈 性能优化

### 1. 调整执行频率
根据服务器负载调整任务频率：
```bash
# 高负载服务器：减少频率
*/60 * * * *  # 每小时执行一次

# 低负载服务器：增加频率  
*/15 * * * *  # 每15分钟执行一次
```

### 2. 错峰执行
避免多个任务同时执行：
```bash
# 错开执行时间
0 2 * * *   # 安全扫描：2点
5 2 * * *   # 数据清理：2点05分
10 2 * * *  # 日志轮转：2点10分
```

### 3. 资源限制
为任务设置超时和资源限制：
```bash
# 使用timeout命令限制执行时间
timeout 300 php think security:scan  # 5分钟超时
```

## 🔄 更新和维护

### 1. 更新定时任务
```bash
# 重新安装定时任务
./scripts/install_crontab.sh

# 或手动编辑
crontab -e
```

### 2. 备份定时任务
```bash
# 备份当前定时任务
crontab -l > crontab_backup_$(date +%Y%m%d).txt
```

### 3. 定期检查
建议每月检查一次：
- 定时任务执行情况
- 日志文件大小
- 系统资源使用
- 告警邮件接收情况

---

**注意**：首次安装后建议观察24小时，确保所有任务正常执行且没有产生过多的系统负载。
